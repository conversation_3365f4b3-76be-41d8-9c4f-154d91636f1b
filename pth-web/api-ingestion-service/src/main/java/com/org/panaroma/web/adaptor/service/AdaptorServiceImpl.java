package com.org.panaroma.web.adaptor.service;

import static com.org.panaroma.commons.constants.CommonConstants.IS_BACKFILLING_FOR_MANDATE_DB;
import static com.org.panaroma.commons.constants.CommonConstants.IS_BACKFILLING_FOR_PTH_DB;
import static com.org.panaroma.commons.constants.CommonConstants.MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES;
import static com.org.panaroma.commons.constants.Constants.RelayConstants.IS_FROM_RELAY;
import static com.org.panaroma.commons.constants.Constants.RelayConstants.ONE;
import static com.org.panaroma.web.adaptor.constants.Constants.ADAPTOR_SERVICE;
import static com.org.panaroma.web.adaptor.constants.Constants.JWT_TOKEN;
import static com.org.panaroma.web.adaptor.constants.Constants.MANDATE;
import static com.org.panaroma.web.adaptor.constants.Constants.SUCCESS;
import static com.org.panaroma.web.adaptor.constants.Constants.SUCCESS_MESSAGE;
import static com.org.panaroma.web.adaptor.constants.Constants.SUCCESS_RESPONSE_CODE;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.INVALID_JWT_TOKEN;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.INVALID_PARAMETER;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.KAFKA_EXCEPTION;
import static com.org.panaroma.web.adaptor.exceptionhandler.ErrorCodeConstants.MAPPER_EXCEPTION;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.API_HIT_COUNT;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.API_NAME;
import static com.org.panaroma.web.adaptor.monitoring.MonitoringConstants.COLON;

import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.kafkaPushAPI.TransactionHistoryDto;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.ConversionUtility;
import com.org.panaroma.commons.utils.LoggerUtility;
import com.org.panaroma.web.adaptor.configuration.kafka.IKafkaProducerManager;
import com.org.panaroma.web.adaptor.dto.BaseResponse;
import com.org.panaroma.web.adaptor.dto.ClientEnum;
import com.org.panaroma.web.adaptor.dto.UpdateTxnStatusApiRequestBody;
import com.org.panaroma.web.adaptor.exceptionhandler.AdaptorException;
import com.org.panaroma.web.adaptor.exceptionhandler.ExceptionFactory;
import com.org.panaroma.web.adaptor.monitoring.MetricsAgent;
import com.org.panaroma.web.adaptor.serializers.IAvroSerializer;
import com.org.panaroma.web.adaptor.utility.EventsValidationUtility;
import com.org.panaroma.web.adaptor.utility.TokenValidatorUtility;
import com.org.panaroma.web.adaptor.validator.ValidationUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.log4j.Log4j2;
import org.apache.avro.generic.GenericData;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Log4j2
public class AdaptorServiceImpl implements IAdaptorService {

	private IKafkaProducerManager kafkaProducerManager;

	private TokenValidatorUtility tokenValidatorUtility;

	private IAvroSerializer<TransactionHistoryDetails> avroSerializer;

	private MetricsAgent metricsAgent;

	private EventsValidationUtility eventsValidationUtility;

	@Autowired
	public AdaptorServiceImpl(final IKafkaProducerManager kafkaProducerManager,
			final TokenValidatorUtility tokenValidatorUtility,
			final IAvroSerializer<TransactionHistoryDetails> avroSerializer, final MetricsAgent metricsAgent,
			final EventsValidationUtility eventsValidationUtility) {
		this.kafkaProducerManager = kafkaProducerManager;
		this.tokenValidatorUtility = tokenValidatorUtility;
		this.avroSerializer = avroSerializer;
		this.metricsAgent = metricsAgent;
		this.eventsValidationUtility = eventsValidationUtility;
	}

	@Override
	public Mono<BaseResponse> pushData(final String client, final TransactionHistoryDto data,
			final Map<String, String> tokens, final boolean isBackFill) throws AdaptorException {

		// pre metrics
		metricsAgent.preProcessMetricsAsync(client, data, isBackFill);

		String systemId = data.getSystemId();

		log.debug("Validation started, systemId {}, time {}", systemId, System.currentTimeMillis());
		ValidationUtil.validate(data);
		log.debug("Validation Finished. systemId {}, time {}", systemId, System.currentTimeMillis());
		TransactionHistoryDetails details = null;
		try {
			details = ConversionUtility.convertToTransactionHistoryDetailObject(data);
			log.debug("Data converted to Kafka POJO. systemId {}, time {}", systemId, System.currentTimeMillis());
		}
		catch (Exception e) {
			log.error("Exception while converting data to kafka compatible object. systemId: {}, Exception {}",
					systemId, CommonsUtility.exceptionFormatter(e));
			throw ExceptionFactory.getException(ADAPTOR_SERVICE, MAPPER_EXCEPTION);
		}

		if (eventsValidationUtility.isUpiEventNeedToBeDiscarded(details)) {
			return Mono.just(new BaseResponse(SUCCESS_MESSAGE, SUCCESS_RESPONSE_CODE, SUCCESS));
		}

		// Validating THD
		ValidationUtil.validate(details);

		// Validate recurring mandate THD
		if (ValidationUtil.isInvalidEventForMandateJourneyFlow(details)) {
			throw ExceptionFactory.getException(ADAPTOR_SERVICE, INVALID_PARAMETER);
		}

		// this.validateJwtToken(client, details, tokens);
		log.debug("JWT Token verified, systemId {}, time {}", systemId, System.currentTimeMillis());

		/*
		 * Though this key is added in contextMap in UpiRelayPreProcessor, I have added it
		 * here as we require the same in mandate pipeline also. This can be removed from
		 * UpiRelayPreProcessor.
		 */
		if (ClientEnum.TSP_UPI.name().equalsIgnoreCase(client)) {
			if (ObjectUtils.isEmpty(details.getContextMap())) {
				details.setContextMap(new HashMap<>());
			}
			details.getContextMap().put(IS_FROM_RELAY, ONE);
		}

		GenericData.Record genericAvroRecord = avroSerializer.serializeToAvroRecord(details);
		log.debug("Kafka Pojo converted to Avro Record systemId {}, time {}", systemId, System.currentTimeMillis());

		String customerId = this.getCustomerId(details);

		String partitionKey = this.getPartitionKey(details);

		BaseResponse baseResponse = new BaseResponse(SUCCESS_MESSAGE, SUCCESS_RESPONSE_CODE, SUCCESS);

		// startTime for kafka sink
		long sinkStartTime = System.currentTimeMillis();

		log.info("Pushing Txn to Kafka Client: {}, TxnType: {}, TxnId: {}", client, details.getTxnType(), systemId);

		log.warn("DTO to be sent to Kafka : {}", details);

		Mono<BaseResponse> mandateResponse = Mono.just(baseResponse);

		/*
		 * TODO : We need to check whether some handling is required if both flags i.e.
		 * isBackfillingForMandateDb & isBackfillingForPthDb are true at the same time. As
		 * per current code if this happens, data won't go to pth_mandate_data topic, it
		 * will only go to pth_upi_data topic
		 */

		if (isTxnTypeValidForMandateJourneyFlow(data) && !isBackfillEventForPthDb(data)) {
			if (details.isParticipantsNotAvailable()) {
				log.warn("Not pushing to mandate kafka as no participant is present. systemId : {}",
						details.getSystemId());
			}
			else {
				mandateResponse = produceMessage(MANDATE, systemId, customerId, genericAvroRecord, isBackFill,
						sinkStartTime, baseResponse, partitionKey);
			}
		}

		/*
		 * Below if block should only be executed if any mandate txn is backfilled only
		 * wrt mandate journey flow & not wrt the PTH flow. If this is not the case, below
		 * if should not be executed. So I have changed the identifier from
		 * isForBackfilling to isBackfillingForMandateDb
		 */

		if (isBackfillEventForMandateDb(data) && !isBackfillEventForPthDb(data)) {
			return Mono.when(mandateResponse).then(Mono.just(baseResponse));
		}

		Mono<BaseResponse> clientResponse = produceMessage(client, systemId, customerId, genericAvroRecord, isBackFill,
				sinkStartTime, baseResponse, partitionKey);

		return Mono.when(mandateResponse, clientResponse).then(Mono.just(baseResponse));
	}

	private Mono<BaseResponse> produceMessage(final String client, final String systemId, final String customerId,
			final GenericData.Record genericAvroRecord, final boolean isBackFill, final long sinkStartTime,
			final BaseResponse baseResponse, final String partitionKey) {
		return this.kafkaProducerManager
			.produceMessage(client, systemId, partitionKey, genericAvroRecord, isBackFill, customerId)
			.doOnError((e) -> {
				log.error("Exception occurred while pushing data to kafka. Exception : {}, System ID : {}",
						CommonsUtility.exceptionFormatter((Exception) e), systemId);
				AdaptorException adaptorException = ExceptionFactory.getException(ADAPTOR_SERVICE, KAFKA_EXCEPTION);
				throw adaptorException;
			})
			.then(Mono.just(baseResponse))
			.flatMap(resp -> {
				// Kafka producing time taken metrics
				long timeTakenForSink = System.currentTimeMillis() - sinkStartTime;
				log.info("Time taken to sink data in kafka for client: {} , timeTaken :{}, txnId : {}", client,
						timeTakenForSink, systemId);
				metricsAgent.kafkaSinkTimeMetricsAsync(timeTakenForSink, client, systemId);
				return Mono.just(resp);
			});
	}

	@Override
	public Mono<BaseResponse> pushData(final String client, final TransactionHistoryDto data,
			final Map<String, String> tokens) throws Exception {
		// pre metrics
		metricsAgent.incrementCount(API_HIT_COUNT, API_NAME + COLON + "pushData");
		return pushData(client, data, tokens, false);
	}

	private void validateJwtToken(final String client, final TransactionHistoryDetails data,
			final Map<String, String> tokens) {
		log.debug("Authorization header : {}", LoggerUtility.createMaskedMap(tokens).toString().replace("\"", "'"));
		String jwtToken = TokenValidatorUtility.getTokenFromMap(tokens, JWT_TOKEN);
		Map<String, String> claims = new HashMap<>();
		claims.put("systemId", data.getSystemId());
		try {
			boolean isValidated = TokenValidatorUtility.verifyJwtToken(ADAPTOR_SERVICE, client, claims, jwtToken);
			if (!isValidated) {
				log.warn("Invalid Jwt token. System ID: {}", data.getSystemId());
				throw ExceptionFactory.getException(ADAPTOR_SERVICE, INVALID_JWT_TOKEN);
			}
		}
		catch (Exception e) {
			log.error("Exception: {} while validating jwt token. System ID: {}", CommonsUtility.exceptionFormatter(e),
					data.getSystemId());
			throw e;
		}
	}

	private String getCustomerId(final TransactionHistoryDetails detail) {
		if (detail.getParticipants() != null) {
			for (TxnParticipants participant : detail.getParticipants()) {
				if (participant.getCustomerId() != null) {
					return participant.getCustomerId();
				}
			}
		}
		return null;
	}

	private String getPartitionKey(final TransactionHistoryDetails detail) {
		if (detail.isParticipantsNotAvailable()) {
			return detail.getSystemId();
		}
		else {
			if (detail.getParticipants() != null) {
				for (TxnParticipants participant : detail.getParticipants()) {
					if (participant.getCustomerId() != null) {
						return participant.getCustomerId();
					}
				}
			}
		}
		return null;
	}

	private boolean isTxnTypeValidForMandateJourneyFlow(final TransactionHistoryDto data) {
		if (Objects.isNull(data)) {
			return false;
		}
		TransactionTypeEnum transactionType = TransactionTypeEnum.getTransactionTypeEnumByName(data.getTxnType());

		if (MANDATE_JOURNEY_FLOW_SUPPORTED_TXN_TYPES.contains(transactionType)) {
			return true;
		}
		return false;
	}

	private boolean isBackfillEventForMandateDb(final TransactionHistoryDto data) {
		if (!isTxnTypeValidForMandateJourneyFlow(data)) {
			return false;
		}
		if (ObjectUtils.isEmpty(data.getContextMap())) {
			return false;
		}
		if (data.getContextMap().containsKey(IS_BACKFILLING_FOR_MANDATE_DB)) {
			return true;
		}
		return false;
	}

	private boolean isBackfillEventForPthDb(final TransactionHistoryDto data) {
		if (ObjectUtils.isEmpty(data.getContextMap())) {
			return false;
		}
		if (data.getContextMap().containsKey(IS_BACKFILLING_FOR_PTH_DB)) {
			return true;
		}
		return false;
	}

	public Mono<BaseResponse> pushUpdateTxnStatusApiData(final UpdateTxnStatusApiRequestBody data,
			final TransactionHistoryDetails thd) {

		GenericData.Record genericAvroRecord = avroSerializer.serializeToAvroRecord(thd);

		String client = data.getTxnSource().name();

		String partitionKey = this.getPartitionKey(thd);

		BaseResponse baseResponse = new BaseResponse(SUCCESS_MESSAGE, SUCCESS_RESPONSE_CODE, SUCCESS);

		return produceMessage(client, thd.getSystemId(), null, genericAvroRecord, false, System.currentTimeMillis(),
				baseResponse, partitionKey);
	}

}
