package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.MID;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_VPA;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_MERCHANT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.QR_ID;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_QR_MERCHANTS;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.REPEAT_PAYMENT_BLOCKING_PAYTM_MERCHANT_VPA_PREFIX_FOR_VPA_BASED_REPEAT_PAYMENT;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.REPEAT_PAYMENT_VPA_BASED_FOR_PAYTM_QR_MERCHANTS_ENABLE;
import com.org.panaroma.web.dto.Client;
import com.org.panaroma.web.utility.DataValidationUtility;

@Component
public class P2mRepeatPaymentStrategy implements RepeatPaymentStrategy {

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Value("${rpt.paymnt.url.p2m.android}")
	String rptPaymntUrlAndroid;

	@Value("${rpt.paymnt.url.p2m.ios}")
	String rptPaymntUrlIos;

	@Value("${rpt.paymnt.url.p2m.upi}")
	String rptPaymntUrlP2mUpi;

	@Value("${rpt.paymnt.url.p2m.paytQR.mid}")
	String rptPaymentUrlPaytmQrForMid;

	@Value("${rpt.paymnt.url.p2m.paytmQR.vpa}")
	String rptPaymentUrlPaytmQrForVpa;

	public static final String URL_NAME = "Pay Again";

	private static Map<String, Boolean> fieldsForUrlForAndroidClient = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForIosClient = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForPaytmQrForMid = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForPaytmQrForVpa = new HashMap<>();

	private static Map<String, Boolean> fieldsForUrlForP2mUpi = new HashMap<>();

	static {
		fieldsForUrlForAndroidClient.put(QR_ID, true);

		fieldsForUrlForIosClient.put(QR_ID, true);

		fieldsForUrlForPaytmQrForMid.put(MID, true);

		fieldsForUrlForPaytmQrForVpa.put(MERCHANT_VPA, true);
		fieldsForUrlForPaytmQrForVpa.put(RPT_PYMNT_MERCHANT_NAME, true);

		fieldsForUrlForP2mUpi.put(RPT_PYMNT_MERCHANT_NAME, true);
		fieldsForUrlForP2mUpi.put(MERCHANT_VPA, true);
		fieldsForUrlForP2mUpi.put(AMOUNT, false);
	}

	@Autowired
	public P2mRepeatPaymentStrategy(ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail walletTxn, final TransformedTransactionHistoryDetail txn,
			final TransformedTransactionHistoryDetail upiTxn, final DetailInputParams detailInputParams) {
		if (DataValidationUtility.isRepeatPaymentLegacyImplEnabled()) {
			if (!Objects.isNull(detailInputParams) && !Objects.isNull(walletTxn)
					&& !Objects.isNull(walletTxn.getContextMap())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
				if (Client.androidapp.name().equals(detailInputParams.getClient())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForAndroidClient, rptPaymntUrlAndroid,
							null, walletTxn, URL_NAME);
				}
				if (Client.iosapp.name().equals(detailInputParams.getClient())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForIosClient, rptPaymntUrlIos, null,
							walletTxn, URL_NAME);
				}
			}

			if (Objects.nonNull(txn) && txn.getContextMap() != null && txn.getContextMap().containsKey(CHANNEL_CODE)
					&& PAYTM_QR_MERCHANTS.equals(txn.getContextMap().get(CHANNEL_CODE))) {
				TransformedParticipant merchantParticipant = null;
				for (TransformedParticipant txnParticipant : txn.getParticipants()) {
					if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(txnParticipant.getEntityType())) {
						merchantParticipant = txnParticipant;
					}
				}
				// if merchantId is present then set merchantId
				if (Objects.nonNull(merchantParticipant) && Objects.nonNull(merchantParticipant.getMerchantData())
						&& Objects.nonNull(merchantParticipant.getMerchantData().getMerchantId())) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForPaytmQrForMid,
							rptPaymentUrlPaytmQrForMid, merchantParticipant, upiTxn, URL_NAME);
				}

				if (configurablePropertiesHolder.getProperty(REPEAT_PAYMENT_VPA_BASED_FOR_PAYTM_QR_MERCHANTS_ENABLE,
						Boolean.class)) {
					// if merchantId is not present then set merchantVpa
					String merchantVpa = "";
					if (Objects.nonNull(merchantParticipant) && Objects.nonNull(merchantParticipant.getUpiData())
							&& org.apache.commons.lang3.StringUtils
								.isNotBlank(merchantParticipant.getUpiData().getVpa())) {
						merchantVpa = merchantParticipant.getUpiData().getVpa();
					}

					String merchantVpaPrefix = configurablePropertiesHolder.getProperty(
							REPEAT_PAYMENT_BLOCKING_PAYTM_MERCHANT_VPA_PREFIX_FOR_VPA_BASED_REPEAT_PAYMENT,
							String.class);

					// merchant blocking prefix is blank or not matching with
					// merchantVpaPrefix
					if (org.apache.commons.lang3.StringUtils.isBlank(merchantVpaPrefix)
							|| (org.apache.commons.lang3.StringUtils.isNotBlank(merchantVpa)
									&& !merchantVpa.startsWith(merchantVpaPrefix))) {
						return GenericUtility.getRepeatPaymentDetails(fieldsForUrlForPaytmQrForVpa,
								rptPaymentUrlPaytmQrForVpa, merchantParticipant, upiTxn, URL_NAME);
					}
				}

				// if Merchant VPA based deeplink is not enable or merchantVpa is not
				// present then return null
				return null;
			}

			/*
			 * Condition for P2M UPI Repeat payment's deeplink upiTxn - not null &&
			 * isSource TRUE && sourceSystem null && txnIndicator - debit
			 */
			if (Objects.nonNull(upiTxn) && Boolean.TRUE.equals(upiTxn.getIsSource())
					&& Objects.isNull(upiTxn.getSourceSystem())
					&& TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(upiTxn.getTxnIndicator())) {
				TransformedParticipant merchantParticipant = null;
				for (TransformedParticipant txnParticipant : upiTxn.getParticipants()) {
					if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(txnParticipant.getEntityType())) {
						merchantParticipant = txnParticipant;
					}
				}
				RepeatPayment repeatPayment = GenericUtility.getRepeatPaymentDetails(fieldsForUrlForP2mUpi,
						rptPaymntUrlP2mUpi, merchantParticipant, upiTxn, URL_NAME);
				return repeatPayment;
			}
		}
		return null;
	}

}