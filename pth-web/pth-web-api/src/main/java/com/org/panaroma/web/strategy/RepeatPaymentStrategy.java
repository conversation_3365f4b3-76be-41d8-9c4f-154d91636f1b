package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;

/**
 * Strategy interface for repeat payment logic abstraction.
 */
public interface RepeatPaymentStrategy {

	RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail walletTxn, final TransformedTransactionHistoryDetail txn,
			final TransformedTransactionHistoryDetail upiTxn, final DetailInputParams detailInputParams);

}