package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.commons.utils.MdcUtility;
import com.org.panaroma.commons.constants.WebConstants;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;

import lombok.extern.log4j.Log4j2;

import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ArrayList;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST;
import static com.org.panaroma.commons.constants.WebConstants.FALSE;
import static com.org.panaroma.commons.constants.WebConstants.IS_DQR;
import static com.org.panaroma.commons.constants.WebConstants.CHANNEL_CODE;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_QR_MERCHANTS;

import static com.org.panaroma.commons.utils.UtilityExtension.isOnlineTxn;
import static com.org.panaroma.commons.utils.UtilityExtension.isOnusTransaction;

@Service
@Log4j2
public class RepeatPaymentService {

	private final P2pInwardRepeatPaymentStrategy p2pInwardRepeatPaymentStrategy;

	private final P2pOutwardRepeatPaymentStrategy p2pOutwardRepeatPaymentStrategy;

	private final P2mRepeatPaymentStrategy p2mRepeatPaymentStrategy;

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	public RepeatPaymentService(P2pInwardRepeatPaymentStrategy p2pInwardRepeatPaymentStrategy,
			P2pOutwardRepeatPaymentStrategy p2pOutwardRepeatPaymentStrategy,
			P2mRepeatPaymentStrategy p2mRepeatPaymentStrategy,
			ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.p2pInwardRepeatPaymentStrategy = p2pInwardRepeatPaymentStrategy;
		this.p2pOutwardRepeatPaymentStrategy = p2pOutwardRepeatPaymentStrategy;
		this.p2mRepeatPaymentStrategy = p2mRepeatPaymentStrategy;
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	public RepeatPayment setRepeatPaymentUrl(final TransformedTransactionHistoryDetail txn,
			DetailInputParams detailInputParams) {

		// Check if repeat payment is disabled for this transaction
		if (isRepeatPaymentDisabled(txn)) {
			log.debug("Repeat payment is disabled for transaction: {} due to status/error code restrictions",
					txn != null ? txn.getTxnId() : "null");
			return null;
		}

		if (isTransactionType(txn, TransactionTypeEnum.P2P_INWARD)) {
			TransformedParticipant debitParticipant = getParticipantByIndicator(txn,
					TransactionIndicator.DEBIT.getTransactionIndicatorKey());
			if (debitParticipant != null) {
				return p2pInwardRepeatPaymentStrategy.getRepeatPaymentDetails(debitParticipant, null, txn, null, null);
			}
		}
		else if (isTransactionType(txn, TransactionTypeEnum.P2P_OUTWARD)) {
			TransformedParticipant creditParticipant = getParticipantByIndicator(txn,
					TransactionIndicator.CREDIT.getTransactionIndicatorKey());
			if (creditParticipant != null) {
				return p2pOutwardRepeatPaymentStrategy.getRepeatPaymentDetails(creditParticipant, null, txn, null,
						null);
			}
		}
		else if (isTransactionType(txn, TransactionTypeEnum.P2M)) {
			Map<String, String> contextMap = txn.getContextMap();
			boolean isDqrFalse = contextMap != null && FALSE.equals(contextMap.get(IS_DQR));
			boolean isPaytmQrMerchant = contextMap != null && contextMap.containsKey(CHANNEL_CODE)
					&& PAYTM_QR_MERCHANTS.equals(contextMap.get(CHANNEL_CODE));

			if ((!isOnlineTxn(txn) && !isOnusTransaction(txn) && isDqrFalse) || isPaytmQrMerchant) {
				try {
					if (Objects.isNull(detailInputParams)) {
						String clientValue = Objects
							.requireNonNullElse(MdcUtility.getConstantValue(WebConstants.CLIENT), "null");
						detailInputParams = DetailInputParams.builder().client(clientValue).build();
					}
					return p2mRepeatPaymentStrategy.getRepeatPaymentDetails(null, null, txn, txn, detailInputParams);
				}
				catch (Exception e) {
					log.warn("Failed to set repeat payment for P2M transaction: {}",
							CommonsUtility.exceptionFormatter(e));
				}
			}
		}
		return null;
	}

	private boolean isTransactionType(final TransformedTransactionHistoryDetail txn,
			final TransactionTypeEnum expectedType) {
		return Objects.nonNull(txn.getTxnType()) && expectedType.getTransactionTypeKey().equals(txn.getTxnType());
	}

	private TransformedParticipant getParticipantByIndicator(TransformedTransactionHistoryDetail txn,
			Integer indicatorKey) {
		if (txn.getParticipants() == null)
			return null;
		for (TransformedParticipant participant : txn.getParticipants()) {
			if (indicatorKey.equals(participant.getTxnIndicator())) {
				return participant;
			}
		}
		return null;
	}

	private boolean isRepeatPaymentDisabled(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			log.debug("Transaction is null, disabling repeat payment");
			return true;
		}
		try {
			List<String> repeatPaymentDisabledStatusList = configurablePropertiesHolder
				.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_LIST, List.class);
			List<String> repeatPaymentDisabledFailureErrorCodesList = configurablePropertiesHolder
				.getProperty(TXN_DETAILS_REPEAT_PAYMENT_CTA_DISABLED_TXN_STATUS_FAILURE_ERROR_CODES_LIST, List.class);
			if (repeatPaymentDisabledStatusList == null) {
				repeatPaymentDisabledStatusList = new ArrayList<>();
			}
			if (repeatPaymentDisabledFailureErrorCodesList == null) {
				repeatPaymentDisabledFailureErrorCodesList = new ArrayList<>();
			}
			if (txn.getStatus() != null) {
				try {
					ClientStatusEnum statusEnum = ClientStatusEnum.getStatusEnumByKey(txn.getStatus());
					if (statusEnum != null && repeatPaymentDisabledStatusList.contains(statusEnum.toString())) {
						log.debug("Repeat payment disabled for transaction: {} due to status: {}", txn.getTxnId(),
								statusEnum);
						return true;
					}
				}
				catch (Exception e) {
					log.warn("Failed to parse status for transaction: {}, status: {}, error: {}", txn.getTxnId(),
							txn.getStatus(), e.getMessage());
				}
			}
			if (txn.getContextMap() != null) {
				String errorCode = txn.getContextMap().get(WebConstants.ERROR_CODE);
				if (errorCode != null && repeatPaymentDisabledFailureErrorCodesList.contains(errorCode)) {
					log.debug("Repeat payment disabled for transaction: {} due to error code: {}", txn.getTxnId(),
							errorCode);
					return true;
				}
			}
			return false;
		}
		catch (Exception e) {
			log.error("Error checking repeat payment disabled status for transaction: {}, error: {}", txn.getTxnId(),
					CommonsUtility.exceptionFormatter(e));
			return true;
		}
	}

}