package com.org.panaroma.web.detailAPI;

import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.WalletType;
import com.org.panaroma.commons.dto.es.TransformedCardData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.web.cstData.CstDataUtility;
import com.org.panaroma.web.dto.detailAPI.DetailApiResponse;
import com.org.panaroma.web.dto.detailAPI.InstrumentDto;
import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.web.utility.DateTimeUtility;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.StatusLogoUtility;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.AC_NO;
import static com.org.panaroma.commons.constants.WebConstants.BANK_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.CARD_NO;
import static com.org.panaroma.commons.constants.WebConstants.CREDIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.DEBIT_CARD;
import static com.org.panaroma.commons.constants.WebConstants.ERROR_MESSAGE;
import static com.org.panaroma.commons.constants.WebConstants.FROM;
import static com.org.panaroma.commons.constants.WebConstants.IN_YOUR;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.ORDER_ID;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOAN;
import static com.org.panaroma.commons.constants.WebConstants.POSTPAID_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.TXN_ID;
import static com.org.panaroma.commons.constants.WebConstants.UPI_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.UPI_REFERENCE_NO;
import static com.org.panaroma.commons.constants.WebConstants.VIA_NET_BANKING;
import static com.org.panaroma.commons.constants.WebConstants.WALLET_TXN_ID;

@Component
@Log4j2
public class P2p2mDetailResponseBuilder extends AbstractTransactionTypeDetailResponseBuilder {

	@Value("${rpt.paymnt.to.mobile.url}")
	String rptPaymntUrl;

	public static final String URL_NAME = "Pay Again";

	private static Map<String, Boolean> fieldsForUrl = new HashMap<>();

	static {
		fieldsForUrl.put(RECIPIENT, true);
	}

	@Override
	public TransactionTypeEnum buildDetailApiResponseFor() {
		return TransactionTypeEnum.P2P2M;
	}

	@Override
	public List<TransactionTypeEnum> listToBuildDetailApiResponseFor() {
		return Arrays.asList(TransactionTypeEnum.P2P2M, TransactionTypeEnum.P2P2M_INWARD,
				TransactionTypeEnum.P2P2M_OUTWARD);
	}

	@Override
	public DetailApiResponse getResponse(final List<TransformedTransactionHistoryDetail> detailList, final String txnId)
			throws Exception {

		TransformedTransactionHistoryDetail txn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (detail.getTxnId().equalsIgnoreCase(txnId)) {
				txn = detail;
			}
		}

		if (txn == null) {
			log.error("No Txn found with input txnId TxnId: {}", txnId);
			throw new RuntimeException("No Txn found with input txnId TxnId: " + txnId);
		}

		DetailApiResponse detailApiResponse = new DetailApiResponse();
		detailApiResponse.setStatus(ClientStatusEnum.getStatusEnumByKey(txn.getStatus()).toString());
		detailApiResponse.setStatusLogoUrl(
				StatusLogoUtility.getStatusLogoUrl(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
		detailApiResponse.setCurrency(Currency.getCurrencyByKey(txn.getCurrency()));
		detailApiResponse.setDateTime(DateTimeUtility.getDateTime(txn.getTxnDate()));
		detailApiResponse.setAmount(Currency.getCurrencyAmountInHigherDenomination(txn.getAmount(), txn.getCurrency()));
		detailApiResponse.setTxnIndicator(String.valueOf(txn.getTxnIndicator()));

		// setting notes in case status is not SUCCESS
		if (!ClientStatusEnum.SUCCESS.equals(ClientStatusEnum.valueOf(detailApiResponse.getStatus()))
				&& txn.getContextMap() != null && txn.getContextMap().containsKey(ERROR_MESSAGE)) {
			detailApiResponse.setNotes(txn.getContextMap().get(ERROR_MESSAGE));
		}

		Map<String, String> refIds = new HashMap<>();
		List<InstrumentDto> creditInstruments = new ArrayList<>();
		List<InstrumentDto> debitInstruments = new ArrayList<>();
		if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(txn.getTxnIndicator())) {
			detailApiResponse
				.setDetailNarration(getNarrationForStatus(ClientStatusEnum.valueOf(detailApiResponse.getStatus())));
			setDataForDebitIndicator(detailList, txn, detailApiResponse, refIds, creditInstruments, debitInstruments);
			// Sender should see receiver instrument as first
			detailApiResponse.setFirstInstrument(creditInstruments);
			detailApiResponse.setSecondInstrument(debitInstruments);
		}
		else {
			detailApiResponse.setDetailNarration("Money Received");
			setDataForCreditIndicator(detailList, txn, detailApiResponse, refIds, creditInstruments, debitInstruments);
			// Receiver should see sender instrument as first
			detailApiResponse.setFirstInstrument(debitInstruments);
			detailApiResponse.setSecondInstrument(creditInstruments);
		}
		refIds.put(ORDER_ID, txn.getOrderId());
		detailApiResponse.setReferenceIdMap(refIds);
		detailApiResponse.setCstorderItem(CstDataUtility.getCstData(txn, detailApiResponse));
		detailApiResponse.setReferenceIds(GenericUtility.convertRefIdMapToList(detailApiResponse.getReferenceIdMap()));

		return detailApiResponse;
	}

	private void setDataForCreditIndicator(final List<TransformedTransactionHistoryDetail> detailList,
			final TransformedTransactionHistoryDetail txn, final DetailApiResponse detailApiResponse,
			final Map<String, String> refIds, final List<InstrumentDto> creditInstruments,
			final List<InstrumentDto> debitInstruments) throws Exception {
		for (TransformedParticipant participant : txn.getParticipants()) {
			InstrumentDto instrumentDto = new InstrumentDto();
			instrumentDto.setCurrency(Currency.getCurrencyByKey(participant.getCurrency()));
			/*
			 * We are using txn.getAmount() instead of participant.getAmount() as we need
			 * to show total amount & only 1 debit instrument to receiver even when
			 * multiple participants are present corresponding to sender
			 */
			instrumentDto.setAmount(Currency.getCurrencyAmountInHigherDenomination(txn.getAmount(), txn.getCurrency()));
			instrumentDto.setInstrumentStatus(participant.getStatus());
			if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& !txn.getEntityId().equals(participant.getEntityId())
					&& !txn.getTxnIndicator().equals(participant.getTxnIndicator())) {
				GenericUtility.setImageData(instrumentDto, participant.getEntityId());
			}
			DetailApiUtil.setParticipantInfo(txn, participant, instrumentDto, buildDetailApiResponseFor(), false);
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				instrumentDto.setNarration(IN_YOUR);
				setData(instrumentDto, participant, detailList, refIds, creditInstruments, true, txn);
				creditInstruments.add(instrumentDto);
			}
			else if (debitInstruments.size() == 0) {
				// acc to which we need not show multiple sender instruments to receiver
				instrumentDto.setNarration(FROM);
				setData(instrumentDto, participant, detailList, refIds, creditInstruments, false, txn);
				instrumentDto.setAdditionalDetail(participant.getRemarks());
				instrumentDto.setIsLogoUrlOfEntity(true);
				// Adding logo Order in 2nd user instrument Dto
				List<Logo> logoOrder = GenericUtility.getListnDetailCommonLogos(participant, txn, true, false, null);
				instrumentDto.setLogoOrder(logoOrder);
				debitInstruments.add(instrumentDto);
			}
		}
	}

	private void setDataForDebitIndicator(final List<TransformedTransactionHistoryDetail> detailList,
			final TransformedTransactionHistoryDetail txn, final DetailApiResponse detailApiResponse,
			final Map<String, String> refIds, final List<InstrumentDto> creditInstruments,
			final List<InstrumentDto> debitInstruments) throws Exception {
		for (TransformedParticipant participant : txn.getParticipants()) {
			InstrumentDto instrumentDto = new InstrumentDto();
			instrumentDto.setCurrency(Currency.getCurrencyByKey(participant.getCurrency()));
			instrumentDto.setAmount(
					Currency.getCurrencyAmountInHigherDenomination(participant.getAmount(), participant.getCurrency()));
			instrumentDto.setInstrumentStatus(participant.getStatus());
			if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& !txn.getEntityId().equals(participant.getEntityId())
					&& !txn.getTxnIndicator().equals(participant.getTxnIndicator())) {
				GenericUtility.setImageData(instrumentDto, participant.getEntityId());
			}
			DetailApiUtil.setParticipantInfo(txn, participant, instrumentDto, buildDetailApiResponseFor(), false);
			if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				instrumentDto.setNarration("To");
				detailApiResponse.setRepeatPayment(getRepeatPaymentDetails(participant, txn));
				instrumentDto.setName(participant.getName());
				Integer merchantTypeKey = (Objects.nonNull(participant.getMerchantData()))
						? participant.getMerchantData().getMerchantType() : null;
				detailApiResponse.setMerchantType(MerchantTypeEnum.getMerchantTypeByKey(merchantTypeKey).toString());
				instrumentDto.setAdditionalDetail(participant.getRemarks());
				instrumentDto.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
						: LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON));
				instrumentDto.setIsLogoUrlOfEntity(true);
				// masking of walletMobileNumber requirement
				if (participant.getWalletData() != null) {
					instrumentDto.setInstrumentDetail(participant.getWalletData().getWalletMobileNumber());
				}
				// Adding logo Order in 2nd user instrument Dto
				List<Logo> logoOrder = GenericUtility.getListnDetailCommonLogos(participant, txn, true, false, null);
				instrumentDto.setLogoOrder(logoOrder);
				creditInstruments.add(instrumentDto);
			}
			else {
				instrumentDto.setNarration("From Your");
				setData(instrumentDto, participant, detailList, refIds, debitInstruments, true, txn);
				debitInstruments.add(instrumentDto);
			}
		}
	}

	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant creditParticipant,
			final TransformedTransactionHistoryDetail txn) {

		if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)) {
			return null;
		}
		if (ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
				&& !Objects.isNull(creditParticipant.getWalletData())) {
			return GenericUtility.getRepeatPaymentDetails(fieldsForUrl, rptPaymntUrl, creditParticipant, txn, URL_NAME);
		}
		return null;
	}

	private String getNarrationForStatus(final ClientStatusEnum status) {
		String narration = "";
		switch (status) {
			case SUCCESS:
				narration = "Money Paid";
				break;
			case FAILURE:
				narration = "Payment Failed";
				break;
			case PENDING:
				narration = "Payment Pending";
				break;
			default:
		}
		return narration;
	}

	private void setData(final InstrumentDto instrumentDto, final TransformedParticipant participant,
			final List<TransformedTransactionHistoryDetail> detailList, final Map<String, String> refIds,
			final List<InstrumentDto> debitInstruments, final boolean isSelf,
			final TransformedTransactionHistoryDetail tthd) throws Exception {
		PaymentSystemEnum paymentSystem = PaymentSystemEnum.getPaymentSystemEnumByKey(participant.getPaymentSystem());
		if (paymentSystem == null) {
			log.error("unexpected payment system for P2P2M detail response, participant entityId:{} ",
					participant.getEntityId());
			throw new RuntimeException("Unexpected payment system P2P2M detail response");
		}

		switch (paymentSystem) {
			case WALLET:
				if (isSelf) {
					instrumentDto
						.setName(WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()));
					instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
				}
				else {
					instrumentDto.setName(participant.getName());
					instrumentDto.setInstrumentDetail(participant.getWalletData().getWalletMobileNumber());
					instrumentDto.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
							: LogoUtility.getLogo(
									WalletTypesEnum.getWalletDisplayName(participant.getWalletData().getWalletType()),
									LogoType.WALLET_ICON));
				}
				refIds.put(WALLET_TXN_ID, participant.getPaymentTxnId());
				break;
			case UPI:
				if (isSelf && Objects.nonNull(participant.getBankData())
						&& StringUtils.isNotBlank(participant.getBankData().getBankName())
						&& StringUtils.isNotBlank(participant.getBankData().getIfsc())
						&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
					instrumentDto.setName(participant.getBankData().getBankName());

					instrumentDto
						.setInstrumentDetail(AC_NO + getMaskedAccountNumber(participant.getBankData().getAccNumber()));
					instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
							participant.getBankData().getBankName()));
				}
				else {
					instrumentDto.setName(participant.getName());
					if (DataValidationUtility.isOtherPartyBankDetailsRequiredForVpa2AccountTxn()
							&& GenericUtilityExtension.isVpa2AccountTxn(tthd)
							&& Objects.nonNull(participant.getBankData())
							&& StringUtils.isNotBlank(participant.getBankData().getBankName())
							&& StringUtils.isNotBlank(participant.getBankData().getIfsc())
							&& StringUtils.isNotBlank(participant.getBankData().getAccNumber())) {
						instrumentDto.setInstrumentDetail(participant.getBankData().getBankName() + " " + AC_NO
								+ getMaskedAccountNumber(participant.getBankData().getAccNumber()));

						instrumentDto.setLogoUrl(
								participant.getLogoUrl() != null ? participant.getLogoUrl() : LogoUtility.getBankLogo(
										participant.getBankData().getIfsc(), participant.getBankData().getBankName()));
					}
					else if (Objects.nonNull(participant.getUpiData())
							&& StringUtils.isNotBlank(participant.getUpiData().getVpa())) {
						instrumentDto.setInstrumentDetail(participant.getUpiData().getVpa());
						instrumentDto.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
								: LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
					}
					else {
						instrumentDto.setLogoUrl(LogoUtility.defaultLogo(PaymentSystemEnum.UPI));
					}
				}
				if (participant.getBankData() != null) {
					refIds.put(UPI_REFERENCE_NO, participant.getBankData().getRrn());
				}
				break;
			case PAYTM_POSTPAID:
				instrumentDto.setName(POSTPAID_LOAN);
				instrumentDto.setLogoUrl(LogoUtility.getLogo(POSTPAID_LOGO, LogoType.WALLET_ICON));
				if (participant.getContextMap() != null) {
					refIds.put(TXN_ID, participant.getContextMap().getOrDefault("postpaid_txn_id", null));
				}
				break;
			case PG:
			case BANK:
				if (isSelf) {
					if (participant.getBankData() != null && participant.getBankData().getBankTxnId() != null) {
						refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
					}
					TransformedCardData cardData = participant.getCardData();
					if (cardData != null) {
						instrumentDto.setInstrumentDetail(CARD_NO + " " + cardData.getCardNum());
						String cardType = CardType.CREDIT.getCardTypeKey().equals(cardData.getCardType()) ? CREDIT_CARD
								: DEBIT_CARD;
						instrumentDto.setName(cardData.getCardIssuer() + " " + cardType);
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(null, cardData.getCardIssuer()));
					}
					else {
						if (Objects.nonNull(participant.getBankData())
								&& StringUtils.isNotBlank(participant.getBankData().getBankName())
								&& StringUtils.isNotBlank(participant.getBankData().getIfsc())) {

							instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
									participant.getBankData().getBankName()));
							instrumentDto.setName(participant.getBankData().getBankName());
							instrumentDto.setInstrumentDetail(VIA_NET_BANKING);
						}
					}
				}
				else {
					if (participant.getBankData() != null && participant.getBankData().getBankTxnId() != null) {
						refIds.put(BANK_REFERENCE_NO, participant.getBankData().getBankTxnId());
					}
					instrumentDto.setName(participant.getName());
					TransformedCardData cardData = participant.getCardData();
					if (cardData != null) {
						instrumentDto.setInstrumentDetail(cardData.getCardIssuer());
						instrumentDto.setLogoUrl(LogoUtility.getBankLogo(null, cardData.getCardIssuer()));
					}
					else {
						if (participant.getBankData() != null
								&& StringUtils.isNotBlank(participant.getBankData().getBankName())
								&& StringUtils.isNotBlank(participant.getBankData().getIfsc())
								&& DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
							instrumentDto.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
									participant.getBankData().getBankName()));
							instrumentDto.setInstrumentDetail(participant.getBankData().getBankName());
						}
					}
				}
				break;
			default:
				log.error("unexpected payment system for P2P2M detail response, participant entityId:{} ",
						participant.getEntityId());
				throw new RuntimeException("Unexpected payment system P2P2M detail response");
		}
	}

	private void setUpiData(final InstrumentDto instrumentDto,
			final List<TransformedTransactionHistoryDetail> detailList, final TransformedParticipant participant,
			final Map<String, String> refIds) {
		TransformedTransactionHistoryDetail txn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(detail.getStreamSource())) {
				txn = detail;
			}
		}

		if (txn == null) {
			if (participant.getUpiData() != null) {
				instrumentDto.setInstrumentDetail(participant.getUpiData().getVpa());
			}
			instrumentDto.setLogoUrl(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
			if (participant.getBankData() != null) {
				instrumentDto.setName(participant.getBankData().getBankName());
			}
			return;
		}

		TransformedParticipant debitUpiParticipant = null;
		for (TransformedParticipant transformedParticipant : txn.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
				.equals(transformedParticipant.getTxnIndicator())) {
				debitUpiParticipant = transformedParticipant;
			}
		}

		instrumentDto.setInstrumentDetail(AC_NO + participant.getBankData().getAccNumber());
		instrumentDto.setLogoUrl(LogoUtility.getLogo(UPI_LOGO, LogoType.OTHER));
		if (debitUpiParticipant != null && debitUpiParticipant.getBankData() != null) {
			instrumentDto.setName(debitUpiParticipant.getBankData().getBankName());
		}
		refIds.put(UPI_REFERENCE_NO, participant.getBankData().getRrn());
	}

	private void setWalletData(final InstrumentDto instrumentDto,
			final List<TransformedTransactionHistoryDetail> detailList, final TransformedParticipant walletParticipant,
			final List<InstrumentDto> debitInstruments, final Map<String, String> refIds) {
		TransformedTransactionHistoryDetail txn = null;

		for (TransformedTransactionHistoryDetail detail : detailList) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(detail.getStreamSource())) {
				txn = detail;
			}
		}

		if (txn == null) {
			if (walletParticipant.getWalletData() != null) {
				instrumentDto
					.setName(WalletTypesEnum.getWalletDisplayName(walletParticipant.getWalletData().getWalletType()));
				instrumentDto.setLogoUrl(LogoUtility.getLogo(instrumentDto.getName(), LogoType.WALLET_ICON));
			}
			else {
				instrumentDto.setName(WalletType.SCLW.toString());
				instrumentDto.setLogoUrl(LogoUtility.getLogo(WalletType.SCLW.toString(), LogoType.WALLET_ICON));
			}
			debitInstruments.add(instrumentDto);
			return;
		}

		for (TransformedParticipant transformedParticipant : txn.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey()
				.equals(transformedParticipant.getTxnIndicator())) {
				InstrumentDto instrumentDto1 = new InstrumentDto();
				instrumentDto1.setNarration("From Your");
				instrumentDto1.setAmount(Currency.getCurrencyAmountInHigherDenomination(
						transformedParticipant.getAmount(), transformedParticipant.getCurrency()));
				instrumentDto1.setCurrency(Currency.getCurrencyByKey(transformedParticipant.getCurrency()));
				instrumentDto1.setName(
						WalletTypesEnum.getWalletDisplayName(transformedParticipant.getWalletData().getWalletType()));
				instrumentDto1.setLogoUrl(LogoUtility.getLogo(instrumentDto1.getName(), LogoType.WALLET_ICON));
				debitInstruments.add(instrumentDto1);
			}
		}
		refIds.put(WALLET_TXN_ID, txn.getTxnId());
	}

}
