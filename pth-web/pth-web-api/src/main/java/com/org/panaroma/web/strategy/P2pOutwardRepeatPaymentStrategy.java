package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.ACC_REF_NUM;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_NAME;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_VPA;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_ENABLED_FOR_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_ENABLED_FOR_VPA2ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.VPA2ACCOUNT;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.VPA2VPA;
import static com.org.panaroma.commons.constants.WebConstants.TXN_INITIATION_MODE;
import static com.org.panaroma.commons.constants.WebConstants.TO_MOBILE_MODE;
import static com.org.panaroma.commons.constants.WebConstants.TO_ACCOUNT_MODE;
import static com.org.panaroma.commons.utils.Utility.isSelfTransferTxn;
import com.org.panaroma.web.utility.DataValidationUtility;
import com.org.panaroma.commons.utils.Utility;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;

@Component
public class P2pOutwardRepeatPaymentStrategy implements RepeatPaymentStrategy {

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	public static final String URL_NAME = "Pay Again";

	@Value("${rpt.payment.url.for.selfTransfer}")
	String rptPaymentUrlForSelfTransfer;

	@Value("${rpt.paymnt.to.mobile.url}")
	String rptPaymntToMobileUrl;

	@Value("${rpt.paymnt.url.p2p.vpa2vpa}")
	String rptPaymntUrlVpa2Vpa;

	@Value("${rpt.paymnt.url.p2p.vpa2account}")
	String rptPaymntUrlVpa2Account;

	@Value("${rpt.paymnt.url.imps.neft.xfer}")
	String rptPaymntUrlForImpsNeftAndXfer;

	private static final Map<String, Boolean> fieldsForVPA2ACCOUNT = new HashMap<>();

	private static final Map<String, Boolean> fieldsForVPA2VPA = new HashMap<>();

	private static final Map<String, Boolean> fieldsForToMobileRptPaymntUrl = new HashMap<>();

	private static final Map<String, Boolean> fieldsForImpsNeftAndXfer = new HashMap<>();

	static {
		fieldsForVPA2ACCOUNT.put(PAYEE_NAME, true);
		fieldsForVPA2ACCOUNT.put(ACC_REF_NUM, true);

		fieldsForVPA2VPA.put(PAYEE_NAME, true);
		fieldsForVPA2VPA.put(PAYEE_VPA, true);
		fieldsForVPA2VPA.put(AMOUNT, false);

		fieldsForToMobileRptPaymntUrl.put(RECIPIENT, true);

		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_NAME, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_ACCT_NUM, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_IFSC, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_BANK_NAME, false);
		fieldsForImpsNeftAndXfer.put(AMOUNT, false);
	}

	@Autowired
	public P2pOutwardRepeatPaymentStrategy(ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail walletTxn, final TransformedTransactionHistoryDetail txn,
			final TransformedTransactionHistoryDetail upiTxn, final DetailInputParams detailInputParams) {
		if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_ENABLED_FOR_SELF_TRANSFER, Boolean.class)) {
			if (isSelfTransferTxn(txn)) {
				return getRepeatPaymentForSelfTransferTxn();
			}
		}

		if (DataValidationUtility.isRepeatPaymentLegacyImplEnabled()) {
			if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
					&& !Objects.isNull(txn.getContextMap()) && !isSelfTransferTxn(txn)) {
				if (configurablePropertiesHolder.getProperty(RPT_PAYMENT_ENABLED_FOR_VPA2ACCOUNT, Boolean.class)
						&& VPA2ACCOUNT.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
					return GenericUtility.getRepeatPaymentDetails(fieldsForVPA2ACCOUNT, rptPaymntUrlVpa2Account,
							participant, txn, URL_NAME);
				}
				if (VPA2VPA.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
					return GenericUtilityExtension.getRepeatPaymentForVpaToVpaCategory(fieldsForVPA2VPA,
							rptPaymntUrlVpa2Vpa, fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl, participant, txn,
							URL_NAME);
				}
			}
		}

		if (!configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
					&& !Objects.isNull(participant.getWalletData())) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl,
						participant, txn, URL_NAME);
			}
		}

		if (TransactionSource.TS.getTransactionSourceKey().equals(txn.getStreamSource())) {
			return getRepeatPaymentForTsTxns(txn);
		}
		return null;
	}

	private RepeatPayment getRepeatPaymentForTsTxns(final TransformedTransactionHistoryDetail tsTxn) {
		if (Objects.nonNull(tsTxn) && ClientStatusEnum.SUCCESS.getStatusKey().equals(tsTxn.getStatus())
				&& Objects.nonNull(tsTxn.getContextMap()) && tsTxn.getContextMap().containsKey(TXN_INITIATION_MODE)) {
			String txnInitiationMode = tsTxn.getContextMap().get(TXN_INITIATION_MODE);
			TransformedParticipant otherParticipant = Utility.getOtherParticipant(tsTxn);
			if (TO_MOBILE_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForToMobileRptPaymntUrl, rptPaymntToMobileUrl,
						otherParticipant, tsTxn, URL_NAME);

			}
			else if (TO_ACCOUNT_MODE.equalsIgnoreCase(txnInitiationMode)) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForImpsNeftAndXfer, rptPaymntUrlForImpsNeftAndXfer,
						otherParticipant, tsTxn, URL_NAME);
			}
		}
		return null;
	}

	private RepeatPayment getRepeatPaymentForSelfTransferTxn() {
		RepeatPayment repeatPayment = new RepeatPayment();
		repeatPayment.setName(URL_NAME);
		repeatPayment.setUrl(rptPaymentUrlForSelfTransfer);
		return repeatPayment;
	}

}