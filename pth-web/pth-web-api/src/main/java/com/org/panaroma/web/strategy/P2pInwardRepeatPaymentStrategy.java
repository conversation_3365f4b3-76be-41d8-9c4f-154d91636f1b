package com.org.panaroma.web.strategy;

import com.org.panaroma.web.dto.detailAPI.RepeatPayment;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.web.dto.detailAPI.DetailInputParams;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.web.utility.GenericUtility;
import com.org.panaroma.web.utility.GenericUtilityExtension;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import static com.org.panaroma.commons.utils.Utility.isSelfTransferTxn;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.org.panaroma.commons.constants.WebConstants.PAYEE_NAME;
import static com.org.panaroma.commons.constants.WebConstants.PAYEE_VPA;
import static com.org.panaroma.commons.constants.WebConstants.AMOUNT;
import static com.org.panaroma.commons.constants.WebConstants.RECIPIENT;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_REMIT_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_ACCT_NUM;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_IFSC;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PYMNT_BENEF_BANK_NAME;
import static com.org.panaroma.commons.constants.WebConstants.RPT_PAYMENT_DISABLED_BANK_AND_WALLET;
import static com.org.panaroma.commons.constants.WebConstants.UPI_TXN_CATEGORY;
import static com.org.panaroma.commons.constants.WebConstants.VPA2VPA;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.DISABLE_REPEAT_PAYMENT_CTA_VPA_LIST_P2P_INWARD_PAYOUT;

@Component
public class P2pInwardRepeatPaymentStrategy implements RepeatPaymentStrategy {

	private final ConfigurablePropertiesHolder configurablePropertiesHolder;

	@Value("${rpt.paymnt.to.mobile.url}")
	String rptPaymntUrlWallet2Wallet;

	@Value("${rpt.paymnt.url.imps.neft.xfer.inward}")
	String rptPaymntUrlForImpsNeftAndXferInward;

	@Value("${rpt.paymnt.url.p2p.vpa2vpa}")
	String rptPaymntUrlVpa2Vpa;

	public static final String URL_NAME = "Pay";

	private static final Map<String, Boolean> fieldsForVPA2VPA = new HashMap<>();

	private static final Map<String, Boolean> fieldsForWalletToWallet = new HashMap<>();

	private static final Map<String, Boolean> fieldsForImpsNeftAndXferInward = new HashMap<>();

	static {

		fieldsForVPA2VPA.put(PAYEE_NAME, true);
		fieldsForVPA2VPA.put(PAYEE_VPA, true);
		fieldsForVPA2VPA.put(AMOUNT, false);

		fieldsForWalletToWallet.put(RECIPIENT, true);

		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_NAME, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_ACCT_NUM, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_IFSC, true);
		fieldsForImpsNeftAndXferInward.put(RPT_PYMNT_REMIT_BANK_NAME, false);
		fieldsForImpsNeftAndXferInward.put(AMOUNT, false);
	}

	private static final Map<String, Boolean> fieldsForImpsNeftAndXfer = new HashMap<>();

	static {
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_NAME, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_ACCT_NUM, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_IFSC, true);
		fieldsForImpsNeftAndXfer.put(RPT_PYMNT_BENEF_BANK_NAME, false);
		fieldsForImpsNeftAndXfer.put(AMOUNT, false);
	}

	@Autowired
	public P2pInwardRepeatPaymentStrategy(ConfigurablePropertiesHolder configurablePropertiesHolder) {
		this.configurablePropertiesHolder = configurablePropertiesHolder;
	}

	@Override
	public RepeatPayment getRepeatPaymentDetails(final TransformedParticipant participant,
			final TransformedTransactionHistoryDetail walletTxn, final TransformedTransactionHistoryDetail txn,
			final TransformedTransactionHistoryDetail upiTxn, final DetailInputParams detailInputParams) {

		if (isRepeatPaymentDisabledForPayoutVpas(txn)) {
			return null;
		}
		if (TransactionSource.UPI.getTransactionSourceKey().equals(txn.getStreamSource())
				&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
				&& Objects.nonNull(txn.getContextMap()) && !isSelfTransferTxn(txn)) {
			if (VPA2VPA.equalsIgnoreCase(txn.getContextMap().get(UPI_TXN_CATEGORY))) {
				return GenericUtilityExtension.getRepeatPaymentForVpaToVpaCategory(fieldsForVPA2VPA,
						rptPaymntUrlVpa2Vpa, fieldsForWalletToWallet, rptPaymntUrlWallet2Wallet, participant, txn,
						URL_NAME);
			}
		}
		if (!configurablePropertiesHolder.getProperty(RPT_PAYMENT_DISABLED_BANK_AND_WALLET, Boolean.class)) {
			if (TransactionSource.WALLET.getTransactionSourceKey().equals(txn.getStreamSource())
					&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus())
					&& Objects.nonNull(participant.getWalletData())) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForWalletToWallet, rptPaymntUrlWallet2Wallet,
						participant, txn, URL_NAME);
			}
			if (TransactionSource.TS.getTransactionSourceKey().equals(txn.getStreamSource())
					&& ClientStatusEnum.SUCCESS.getStatusKey().equals(txn.getStatus()) && txn.getContextMap() != null) {
				return GenericUtility.getRepeatPaymentDetails(fieldsForImpsNeftAndXferInward,
						rptPaymntUrlForImpsNeftAndXferInward, participant, txn, URL_NAME);

			}
		}
		return null;
	}

	// For Jira ->PTH-718
	private boolean isRepeatPaymentDisabledForPayoutVpas(final TransformedTransactionHistoryDetail txn) {
		// Payout CTA are Payouts from paytm (including setllements, gold sell and
		// cashbacks)
		// if we receive a P2P_inward from these VPAs mentioned in list, we should not
		// show Pay CTA.
		List<String> listOfDisableVpa = configurablePropertiesHolder
			.getProperty(DISABLE_REPEAT_PAYMENT_CTA_VPA_LIST_P2P_INWARD_PAYOUT, List.class);
		List<TransformedParticipant> participants = txn.getParticipants();
		for (TransformedParticipant participant : participants) {
			// first check is to fetch the Debit participant,second check is to find
			// whether its vpa belongs to listOfDisableVpa or not
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (participant.getUpiData() != null && listOfDisableVpa.contains(participant.getUpiData().getVpa())) {
					return true;
				}
			}
		}
		return false;
	}

}