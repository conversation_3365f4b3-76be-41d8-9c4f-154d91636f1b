package com.org.panaroma.web.utility;

import static com.org.panaroma.commons.constants.BankDataConstants.PPBL_TS_IMPS_REPORT_CODES;
import static com.org.panaroma.commons.constants.BankDataConstants.VISA_RECURRING_REPORT_CODE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.IS_SEARCHABLE_STRING_ENABLE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV1Constants.SET_MCC_CODE_IN_RESPONSE;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.WebApiV2Constants.PTL_START_API_VERSION;
import static com.org.panaroma.commons.constants.Constants.UPI_TXN_ID;
import static com.org.panaroma.commons.constants.Constants.IS_SELF_TRANSFER;
import static com.org.panaroma.commons.constants.WebConstants.API;
import static com.org.panaroma.commons.constants.WebConstants.API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.CST_SUB_CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.DYNAMIC_IDENTIFIER;
import static com.org.panaroma.commons.constants.WebConstants.FD_REPORT_CODES;
import static com.org.panaroma.commons.constants.WebConstants.FE_REPORTED_RECEIVER_CUST_ID;
import static com.org.panaroma.commons.constants.WebConstants.GRP_ID_SEPARATOR;
import static com.org.panaroma.commons.constants.WebConstants.IS_RECENT_PAGE_LISTING;
import static com.org.panaroma.commons.constants.WebConstants.LISTING;
import static com.org.panaroma.commons.constants.WebConstants.LISTING_CUSTOM_NARRATION;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_DEFAULT_LOGO;
import static com.org.panaroma.commons.constants.WebConstants.MERCHANT_NAME;
import static com.org.panaroma.commons.constants.WebConstants.P2M_CARD_LOGO_RPT_CODES;
import static com.org.panaroma.commons.constants.WebConstants.PASSBOOK_TYPE;
import static com.org.panaroma.commons.constants.WebConstants.PAYTMGIFTVOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_GIFT_VOUCHER;
import static com.org.panaroma.commons.constants.WebConstants.PAYTM_MERCHANT;
import static com.org.panaroma.commons.constants.WebConstants.PPBL_TXN_TYPES_FOR_COMMON_LOGOS;
import static com.org.panaroma.commons.constants.WebConstants.RECENT_TXNS_API;
import static com.org.panaroma.commons.constants.WebConstants.REPORT_CODE;
import static com.org.panaroma.commons.constants.WebConstants.SEARCH_API_VERSION;
import static com.org.panaroma.commons.constants.WebConstants.STREAM_SOURCE_SEPARATOR;
import static com.org.panaroma.commons.constants.WebConstants.SUB_CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.TRUE;
import static com.org.panaroma.commons.constants.WebConstants.TXN_DATE_SEPARATOR;
import static com.org.panaroma.commons.constants.WebConstants.TXN_HISTORY_USER_IMAGE_CLIENT;
import static com.org.panaroma.commons.constants.WebConstants.UPI_INTERNATIONAL_REPORT_CODE;
import static com.org.panaroma.web.constants.metadataconstants.Constants.SEARCH_API_UPDATES_DATE_SET_WHEN_DIFF_IN_DAYS;
import static com.org.panaroma.web.monitoring.MonitoringConstants.ADD_MONEY_USER_INSTRUMENT_LOGO_V2_POPULATED_IS_NULL;
import static com.org.panaroma.web.utility.GenericUtility.getTxnPurpose;
import static com.org.panaroma.web.utility.GenericUtilityExtension.amountFormatForSeachableArray;
import static com.org.panaroma.web.utility.TransactionHistoryServiceUtility.addMissingImageUrlUserIdOfSecondParty;

import com.org.panaroma.commons.constants.BankDataConstants;
import com.org.panaroma.commons.constants.ConfigPropertiesEnum;
import com.org.panaroma.commons.dto.ClientStatusEnum;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.KafkaUrlImageData;
import com.org.panaroma.commons.dto.Logo;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.TxnCategoryEnum;
import com.org.panaroma.commons.dto.UserDetails;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTag;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedWalletData;
import com.org.panaroma.commons.dto.webApi.RepoResponseSearchApiDto;
import com.org.panaroma.commons.enums.ApiVersion;
import com.org.panaroma.commons.enums.LogoType;
import com.org.panaroma.commons.enums.PthVersion;
import com.org.panaroma.commons.enums.WalletTypesEnum;
import com.org.panaroma.commons.kafka.IKafkaClient;
import com.org.panaroma.commons.utils.BeanUtil;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.LogoUtility;
import com.org.panaroma.commons.utils.PthVersionUtility;
import com.org.panaroma.commons.utils.UpiLiteUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.commons.utils.UtilityExtension;
import com.org.panaroma.rule.engine.service.RuleEngineService;
import com.org.panaroma.web.cache.ICacheClient;
import com.org.panaroma.web.config.BankConfig;
import com.org.panaroma.web.config.BankDataConfigEnum;
import com.org.panaroma.web.config.RptCodeConfig;
import com.org.panaroma.web.dto.ColourCodeTxnTypeEnum;
import com.org.panaroma.web.dto.EsResponseTxn;
import com.org.panaroma.web.dto.InstrumentInfo;
import com.org.panaroma.web.dto.LogoOrderUtility;
import com.org.panaroma.web.dto.SearchFilters;
import com.org.panaroma.web.dto.SecondPartyInfo;
import com.org.panaroma.web.dto.listing.ContextualInfo;
import com.org.panaroma.web.enums.ListingResponseMappingEnum;
import com.org.panaroma.web.mapper.ISecondPartyMapper;
import com.org.panaroma.web.mapper.P2pInwardRemittanceSecondPartyMapper;
import com.org.panaroma.web.mapper.UpiWalletCreditReversalSecondPartyMapper;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.monitoring.MonitoringConstants;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import com.org.panaroma.web.strategy.RepeatPaymentService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Log4j2
@RequiredArgsConstructor
@Component
public class ListingUtility {

	private static final List<String> p2mCardLogoRptCodes;

	private static final HashSet<String> fdReportCodeSet;

	private static final HashSet<String> upiInternationalReportCodeSet;

	private static RuleEngineService ruleEngineService;

	private static MetricsAgent metricAgent;

	private static IKafkaClient iKafkaClient;

	private static ICacheClient cacheClient;

	private static ConfigurablePropertiesHolder configurablePropertiesHolder;

	private static ListingResponseModifier listingResponseModifier;

	private static RepeatPaymentService repeatPaymentService;

	@Autowired
	public ListingUtility(final RuleEngineService ruleEngineService, final MetricsAgent metricsAgent,
			final IKafkaClient iKafkaClient, final ICacheClient cacheClient,
			final ConfigurablePropertiesHolder configurablePropertiesHolder,
			final ListingResponseModifier listingResponseModifier, final RepeatPaymentService repeatPaymentService) {
		ListingUtility.ruleEngineService = ruleEngineService;
		ListingUtility.metricAgent = metricsAgent;
		ListingUtility.iKafkaClient = iKafkaClient;
		ListingUtility.cacheClient = cacheClient;
		ListingUtility.configurablePropertiesHolder = configurablePropertiesHolder;
		ListingUtility.listingResponseModifier = listingResponseModifier;
		ListingUtility.repeatPaymentService = repeatPaymentService;
	}

	private static final Map<TransactionTypeEnum, ISecondPartyMapper> transactionTypeEnumISecondPartyMapperMap = new EnumMap<>(
			TransactionTypeEnum.class);

	static {
		fdReportCodeSet = new HashSet<>(Arrays.asList(FD_REPORT_CODES.split("\\|")));
		upiInternationalReportCodeSet = new HashSet<>(Arrays.asList(UPI_INTERNATIONAL_REPORT_CODE.split("\\|")));
		p2mCardLogoRptCodes = Arrays.asList(P2M_CARD_LOGO_RPT_CODES.split("\\|"));
		transactionTypeEnumISecondPartyMapperMap.put(TransactionTypeEnum.P2P_INWARD_REMITTANCE,
				new P2pInwardRemittanceSecondPartyMapper());
		transactionTypeEnumISecondPartyMapperMap.put(TransactionTypeEnum.UPI_WALLET_CREDIT_REVERSAL,
				new UpiWalletCreditReversalSecondPartyMapper());
	}

	public static Map<String, UserDetails> getStringUserDetailsMap(final boolean pushMissingUserImageListToKafka,
			final boolean fetchUserImageFromCache, final ICacheClient cacheClient, final IKafkaClient kafkaClient,
			final RepoResponseSearchApiDto repoResponse) {
		try {
			List<KafkaUrlImageData> missingImageUrlUserIdKafkaList = new ArrayList<>();
			Map<String, UserDetails> userIdImageUrlMapFromCache = new HashMap<>(0);
			repoResponse.getTransformedTransactionHistoryDetailsIterable().forEach(tthd -> {
				for (TransformedParticipant participant : tthd.getParticipants()) {
					// getting second party entityId
					addMissingImageUrlUserIdOfSecondParty(missingImageUrlUserIdKafkaList, tthd, participant);
				}
			});

			List<String> missingImageUserIdList = new ArrayList<>();
			for (KafkaUrlImageData kafkaUrlImageData : missingImageUrlUserIdKafkaList) {
				missingImageUserIdList.add(kafkaUrlImageData.getUserId());
			}

			if (fetchUserImageFromCache && !missingImageUserIdList.isEmpty()) {
				setUserImageUrlFromCache(missingImageUserIdList, userIdImageUrlMapFromCache, cacheClient);
			}

			List<KafkaUrlImageData> listForFetchingUserImageFromKafka = new ArrayList<>();

			Set<String> userImageDetailsInCacheSet = userIdImageUrlMapFromCache.keySet();
			for (KafkaUrlImageData kafkaUrlImageData : missingImageUrlUserIdKafkaList) {
				if (!userImageDetailsInCacheSet.contains(kafkaUrlImageData.getUserId())) {
					listForFetchingUserImageFromKafka.add(kafkaUrlImageData);
				}
			}
			metricAgent.countMetricsWithCountTypeString(MonitoringConstants.USER_IMAGE_URL_RECORDS_IN_CACHE,
					(long) userImageDetailsInCacheSet.size());
			metricAgent.countMetricsWithCountTypeString(MonitoringConstants.USER_IMAGE_URL_RECORDS_MISSING_IN_CACHE,
					(long) listForFetchingUserImageFromKafka.size());

			log.debug("pushing Missing UserImageList flag is : {}", pushMissingUserImageListToKafka);
			if (pushMissingUserImageListToKafka && !missingImageUrlUserIdKafkaList.isEmpty()) {
				// push in kafka
				log.info("pushing Missing UserImageList To Kafka with size: {}",
						listForFetchingUserImageFromKafka.size());
				pushMissingImageUrlUserIdListToKafka(listForFetchingUserImageFromKafka, kafkaClient);
			}
			return userIdImageUrlMapFromCache;
		}
		catch (Exception e) {
			log.error("Error while creating userIdImageUrlMapFromCache. Exception: {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

	private static void setUserImageUrlFromCache(final List<String> missingImageUrlUserIdList,
			final Map<String, UserDetails> userIdImageUrlMapFromCache, final ICacheClient cacheClient) {
		try {
			userIdImageUrlMapFromCache.putAll(cacheClient.getUserImageUrlMapFromCache(missingImageUrlUserIdList));
		}
		catch (Exception e) {
			log.error("Error while fetching image url from cache. Exception: {}", CommonsUtility.exceptionFormatter(e));
		}
	}

	private static void pushMissingImageUrlUserIdListToKafka(final List<KafkaUrlImageData> missingImageUrlUserIdList,
			final IKafkaClient kafkaClient) {
		try {
			final Map<String, Object> kafkaUrlImageDataMap = new HashMap<>();
			// need to check for non-blocking call
			for (KafkaUrlImageData kafkaUrlImageData : missingImageUrlUserIdList) {
				kafkaUrlImageDataMap.put(kafkaUrlImageData.getUserId(), kafkaUrlImageData);
			}
			kafkaClient.pushIntoKafka(TXN_HISTORY_USER_IMAGE_CLIENT, kafkaUrlImageDataMap);
		}
		catch (Exception e) {
			log.error("Error while pushing missingImageUrlUserIdList to kafka. Exception: {}",
					CommonsUtility.exceptionFormatter(e));
		}
	}

	public static List<EsResponseTxn> getEsResponseTxnIterable(
			final List<TransformedTransactionHistoryDetail> esDtoList, final String langId,
			final List<String> salaryReportCodes, final Map<String, UserDetails> userIdImageUrlMapFromCache,
			final Map<String, String> paramMap) {

		List<EsResponseTxn> transactionList = new ArrayList<>();
		for (TransformedTransactionHistoryDetail esDto : esDtoList) {
			transactionList
				.add(convertToEsResponseTxn(esDto, langId, salaryReportCodes, userIdImageUrlMapFromCache, paramMap));
		}
		return transactionList;
	}

	public static EsResponseTxn convertToEsResponseTxn(final TransformedTransactionHistoryDetail esDto,
			final String langId, final List<String> salaryReportCodes,
			final Map<String, UserDetails> userIdImageUrlMapFromCache, final Map<String, String> paramMap) {
		// Remove ignored participant from View end
		Utility.removeIgnoredParticipantFromView(esDto);
		GenericUtility.sortParticipants(esDto);

		EsResponseTxn esResponseTxn = new EsResponseTxn();
		esResponseTxn.setCurrency(Currency.getCurrencyByKey(esDto.getCurrency()));
		if (esDto.getContextMap() != null && salaryReportCodes.contains(esDto.getContextMap().get(REPORT_CODE))) {
			esResponseTxn.setMaskAmount(true);
		}
		esResponseTxn.setAmount(GenericUtilityExtension.setAmount(esDto, esResponseTxn, paramMap));
		/**
		 * Need to show all sub-wallet txns in wallet passbook. There will be txns with
		 * multiple sub-wallets involved, so we can not show closing balance for each
		 * sub-wallet used. So, we would stop showing closing balance in wallet passbook,
		 * will instead show instrument narration with logo same as UTH passbook.
		 */
		// esResponseTxn.setClosingBalance(GenericUtilityExtension.getClosingBalance(esDto,
		// paramMap.get(WALLET_TYPE)));
		esResponseTxn.setUserInstrumentLogos(GenericUtility.getUserInstrumentLogos(esDto));
		esResponseTxn.setUserInstrumentLogosV2(GenericUtility.getUserInstrumentLogosV2(esDto));
		esResponseTxn.setTxnIndicator(GenericUtility.getTxnIndicator(esDto));
		esResponseTxn.setTxnDate(esDto.getTxnDate());
		// getting status from common method
		ClientStatusEnum status = GenericUtility.getStatusForListing(esDto);
		// Specific check for cst panel request.
		if (Objects.nonNull(paramMap.get("cstService"))) {
			status = ClientStatusEnum.getStatusEnumByKey(esDto.getStatus());
		}
		// setting status tag here
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(esDto.getStreamSource())
				&& esDto.getContextMap() != null
				&& PPBL_TS_IMPS_REPORT_CODES.contains(esDto.getContextMap().get(BankDataConstants.REPORT_CODE))) {
			// Setting this was due to lack of options in current code setup
			status = ClientStatusEnum.getStatusEnumByKey(esDto.getStatus());
		}
		esResponseTxn.setStatus(GenericUtility.getStatusLabel(status));
		esResponseTxn.setStatusKey(String.valueOf(status.getStatusKey()));
		// Custom Narration in Search
		String listingCustomNarration = null;

		for (TransformedParticipant transformedParticipant : esDto.getParticipants()) {
			// If contextMap->listingCustomNarration in merchant not null or not empty or
			// does not contains empty spacies
			// then add custom narrations
			// and secondPartyName should be send null
			// Else continue the normal flow
			if (listingCustomNarration == null) {
				listingCustomNarration = GenericUtility.getNarration(transformedParticipant, esDto.getEntityId(),
						LISTING_CUSTOM_NARRATION);
				if (listingCustomNarration != null) {
					break;
				}
			}
		}
		esResponseTxn.setSecondPartyInfo(
				getSecondPartyInfo(esDto, esResponseTxn.getStatus(), userIdImageUrlMapFromCache, false));
		// add UTH Category Logo in logoOrder for UPi Lite Txn
		if (Objects.nonNull(esResponseTxn.getSecondPartyInfo()) && UpiLiteUtility.isUpiLiteTxn(esDto)
				&& TransactionTypeEnum.P2M.getTransactionTypeKey().equals(esDto.getTxnType())) {
			// get logos for 2nd user
			// special handling for logo Order and LogUrl for UPI Lite Txn
			// Issue : When we open merchant Logo sent by UPI In UPI doc, its throw 404.
			// As we are going to power UPI Lite Passbook only by UPI Doc to overCome the
			// issue of merging
			// This issue 404 will come there.
			// Solution : In case txn is UpiLite and Transaction Source is UPI
			// Set Default Logo in logoUrl
			// Add special handling for UPi Lite LogoOrder in getListDetailCommonLogos.
			// Todo : will plan to make this change general for Detail response which is
			// made using UPI doc

			// Default logo Url
			esResponseTxn.getSecondPartyInfo().setLogoUrl(LogoUtility.setDefaultLogoUrlForUpiLiteP2m());

			// setLogo Order
			esResponseTxn.getSecondPartyInfo().setLogoOrder(LogoUtility.setLogoOrderForUpiLiteP2m(esDto));
		}
		String recentNarration = "";
		boolean isRecentPageListing = Boolean.parseBoolean(paramMap.get(IS_RECENT_PAGE_LISTING));
		if (isRecentPageListing) {
			recentNarration = GenericUtilityExtension.setRecentNarration(esDto, status, esResponseTxn);
			if (StringUtils.isNotBlank(recentNarration)) {
				esResponseTxn.setNarration(recentNarration);
			}
		}
		String nameAlreadyAddedInTheNarrationField = null;
		if (StringUtils.isEmpty(recentNarration)) {
			if (listingCustomNarration != null) {
				esResponseTxn.setNarration(listingCustomNarration);
				// esResponseTxn.getSecondPartyInfo().setName(null);
			}
			else if (esDto.getIsBankData() && !Utility.isImpsFromTs(esDto)) {
				esResponseTxn
					.setNarration(setBankData(esDto, esResponseTxn.getSecondPartyInfo(), esResponseTxn.getStatus(),
							userIdImageUrlMapFromCache, isRecentPageListing, esResponseTxn, true));
				String narration = esResponseTxn.getNarration();
				if (Objects.nonNull(esResponseTxn.getSecondPartyInfo()) && StringUtils.isNotBlank(narration)
						&& "1".equalsIgnoreCase(langId) && narration.contains(DYNAMIC_IDENTIFIER)) {
					esResponseTxn.setNarration(
							narration.replace(DYNAMIC_IDENTIFIER, esResponseTxn.getSecondPartyInfo().getName()));
					nameAlreadyAddedInTheNarrationField = esResponseTxn.getSecondPartyInfo().getName();
					// esResponseTxn.getSecondPartyInfo().setName(null);
				}
			}
			else if (esDto.getIsBankData() && Utility.isImpsFromTs(esDto)) {
				esResponseTxn.getSecondPartyInfo().setLogoUrl(LogoUtility.getBankCategoryLogo(esDto.getTxnType()));
				esResponseTxn.setNarration(GenericUtility.getNarrationLabel(esDto, status, esResponseTxn));
			}
			else {
				esResponseTxn.setNarration(GenericUtility.getNarrationLabel(esDto, status, esResponseTxn));
			}
		}
		esResponseTxn.setDateLabel(GenericUtility.getDateLabel(String.valueOf(esDto.getTxnDate())));
		esResponseTxn.setTimeLabel(GenericUtility.getTimeLabel(String.valueOf(esDto.getTxnDate())));

		esResponseTxn.setTxnId(getModifiedTxnId(esDto));
		if (paramMap.getOrDefault(SEARCH_API_VERSION, ApiVersion.v3.name())
			.compareToIgnoreCase(ApiVersion.v3.name()) >= 0) {

			esResponseTxn.setSourceTxnId(getSourceTxnId(esDto));

			// sending as null for PTL as it is being served from cache and docUpdated
			// isnt consistent with value in db
			if (RECENT_TXNS_API.equals(paramMap.get(API))) {
				esResponseTxn.setDocUpdatedDate(null);
			}
			else {
				esResponseTxn.setDocUpdatedDate(getDocUpdatedDate(esDto, paramMap));
			}
		}
		esResponseTxn
			.setStreamSource(String.valueOf(TransactionSource.getTransactionSourceEnumByKey(esDto.getStreamSource())));
		esResponseTxn.setStreamSourceKey(esDto.getStreamSource().toString());

		if (configurablePropertiesHolder.getProperty(ConfigPropertiesEnum.LISTING_TAG_ENABLED, Boolean.class)) {
			if (CollectionUtils.isNotEmpty(esDto.getTags())) {
				esResponseTxn
					.setTxnTags(esDto.getTags().stream().map(TransformedTag::getTag).collect(Collectors.toList()));
			}
		}

		CtasNodeUtility.setListingCtaMap(esDto, esResponseTxn);

		// Check if repeat payment is globally enabled
		Boolean isRepeatPaymentEnabled = configurablePropertiesHolder
			.getProperty(ConfigPropertiesEnum.LISTING_REPEAT_PAYMENT_ENABLED, Boolean.class);
		if (Boolean.TRUE.equals(isRepeatPaymentEnabled)) {
			log.debug("Repeat payment is enabled for listing via configuration: {} = {}",
					ConfigPropertiesEnum.LISTING_REPEAT_PAYMENT_ENABLED.getKey(), isRepeatPaymentEnabled);
			esResponseTxn.setRepeatPaymentUrl(Optional.ofNullable(repeatPaymentService.setRepeatPaymentUrl(esDto, null))
				.map(rp -> rp.getUrl())
				.orElse(null));
		}

		if (paramMap != null && CST_SUB_CLIENT.equalsIgnoreCase(paramMap.get(SUB_CLIENT))) {
			String avenueOrderId = GenericUtilityExtension.getAvenueOrderIdFromContextMap(esDto.getContextMap());
			esResponseTxn.setAvenueOrderId(avenueOrderId);
		}

		// Sending ptl & searchFilters objects for API_VERSION >= 2.0 or configured value
		if (PthVersionUtility.isRequestPthVersionGreaterThanOrEqualTo(PthVersion.V_1_0) || UtilityExtension
			.getApiVersion(paramMap) >= configurablePropertiesHolder.getProperty(PTL_START_API_VERSION, Double.class)) {
			setInstrumentInfo(esDto, esResponseTxn);
			setSearchFilters(esDto, esResponseTxn);
		}

		// Applying rules on listing response. Do not apply rules on recent page.
		if (!Boolean.TRUE.equals(isRecentPageListing)) {
			ruleEngineService.applyRules(esDto, esResponseTxn);
		}
		ruleEngineService.applyRulesWithContext(esDto, esResponseTxn, paramMap.get(PASSBOOK_TYPE));
		ColourCodeTxnTypeEnum colourCodeTxnType = TransactionHistoryServiceUtility.getColorCodeForTxnType(esDto);
		esResponseTxn.setTxnAmountColour(colourCodeTxnType.getColourCode().toString());
		LogoOrderUtility.sortLogoOrder(esResponseTxn.getSecondPartyInfo().getLogoOrder(),
				TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType()));

		if (TransactionTypeEnum.ADD_MONEY.getTransactionTypeKey().equals(esDto.getMainTxnType())) {
			metricAgent.pushSingleDocMetricsWhileServingAddMoneyListing(esDto);
			if (Objects.nonNull(esResponseTxn) && Objects.nonNull(esResponseTxn.getUserInstrumentLogosV2())
					&& esResponseTxn.getUserInstrumentLogosV2().size() == 1
					&& StringUtils.isBlank(esResponseTxn.getUserInstrumentLogosV2().get(0).getLogoUrl())) {
				metricAgent.incrementCount(ADD_MONEY_USER_INSTRUMENT_LOGO_V2_POPULATED_IS_NULL);
			}
		}

		// This param will be passed by FE in search & updates APIs where updated response
		// is required
		if (PthVersionUtility.isRequestPthVersionGreaterThanOrEqualTo(PthVersion.V_1_0)
				|| paramMap.containsKey(API_VERSION)) {
			if (StringUtils.isNotBlank(listingCustomNarration)) {
				/*
				 * We need to show this narration to the user no need to show
				 * dateTimeLabel in this case as the narration already has the verb in it
				 */
				listingResponseModifier.updateUserInstrumentRelatedInfo(esResponseTxn, esDto);
			}
			else {
				if (StringUtils.isNotBlank(nameAlreadyAddedInTheNarrationField)) {
					// We need this name to be used in modifed narration, so setting it
					// back to secondPartyInfo.name
					esResponseTxn.getSecondPartyInfo().setName(nameAlreadyAddedInTheNarrationField);
				}
				listingResponseModifier.modifyResponse(esResponseTxn, esDto, paramMap);
			}
		}

		setSearchAbleStringInResponse(esDto, esResponseTxn, paramMap);
		setMccInResponse(esDto, esResponseTxn);
		esResponseTxn.setTxnType(esDto.getMainTxnType());

		// Adding contextual information to the transaction
		ContextualInfo contextualInfo = new ContextualInfo();
		contextualInfo
			.setFlowType(UtilityExtension.getTxnFlowType(esDto, esResponseTxn.getSecondPartyInfo().getParticipant()));
		if (Objects.nonNull(esDto.getContextMap())
				&& StringUtils.isNotBlank(esDto.getContextMap().get(FE_REPORTED_RECEIVER_CUST_ID))) {
			contextualInfo.setFeReportedReceiverCustId(esDto.getContextMap().get(FE_REPORTED_RECEIVER_CUST_ID));
		}
		esResponseTxn.setContextualInfo(contextualInfo);

		if (PthVersionUtility.isRequestPthVersionGreaterThanOrEqualTo(PthVersion.V_4_0)) {
			esResponseTxn
				.setIsHiddenTxn(Objects.isNull(esDto.getIsHiddenTxn()) ? Boolean.FALSE : esDto.getIsHiddenTxn());
		}

		// As response logs are getting printed using Logbook. search "Logbook" &&
		// "response" to check these logs.
		/*
		 * log.info("Listing API Response for entityId: {}  -> response: {}",
		 * esDto.getEntityId(), esResponseTxn);
		 */
		return esResponseTxn;
	}

	private static void setSearchAbleStringInResponse(final TransformedTransactionHistoryDetail esDto,
			final EsResponseTxn esResponseTxn, final Map<String, String> paramMap) {
		Boolean isSearchAbleStringEnabled = configurablePropertiesHolder.getProperty(IS_SEARCHABLE_STRING_ENABLE,
				Boolean.class);
		if (!isSearchAbleStringEnabled || esDto == null) {
			return;
		}

		// set searchAble string in esResponseTxn
		List<String> searchAbleStringList = new ArrayList<>();

		if (esDto.getAmount() != null) {
			String amt = GenericUtilityExtension.setAmount(esDto, esResponseTxn, paramMap);
			searchAbleStringList.add(amountFormatForSeachableArray(amt));
		}

		if (esDto.getSearchFields() != null) {

			if (StringUtils.isNotBlank(esDto.getSearchFields().getSearchOtherName())) {
				searchAbleStringList.add(esDto.getSearchFields().getSearchOtherName());
			}

			if (StringUtils.isNotBlank(esDto.getSearchFields().getSearchOtherMobileNo())) {
				searchAbleStringList.add(esDto.getSearchFields().getSearchOtherMobileNo());
			}
			if (ObjectUtils.isNotEmpty(esDto.getSearchFields().getSearchUthMerchantCategory())) {
				searchAbleStringList.addAll(esDto.getSearchFields()
					.getSearchUthMerchantCategory()
					.stream()
					.filter(val -> !(UtilityExtension.isNumeric(val)))
					.toList());
			}

			if (ObjectUtils.isNotEmpty(esDto.getSearchFields().getSearchTags())) {
				searchAbleStringList.addAll(esDto.getSearchFields().getSearchTags());
			}

			if (StringUtils.isNotBlank(esDto.getSearchFields().getSearchOtherVpa())) {
				searchAbleStringList.add(esDto.getSearchFields().getSearchOtherVpa());
			}

			if (StringUtils.isNotBlank(esDto.getSearchFields().getSearchReferenceId())) {
				searchAbleStringList.add(esDto.getSearchFields().getSearchReferenceId());
			}

			if (StringUtils.isNotBlank(esDto.getSearchFields().getSearchOrderId())) {
				searchAbleStringList.add(esDto.getSearchFields().getSearchOrderId());
			}

		}

		if (!searchAbleStringList.isEmpty()) {
			esResponseTxn.setSearchAbleStrings(searchAbleStringList);
		}

	}

	private static void setMccInResponse(final TransformedTransactionHistoryDetail esDto,
			final EsResponseTxn esResponseTxn) {
		Boolean setMccInResponse = configurablePropertiesHolder.getProperty(SET_MCC_CODE_IN_RESPONSE, Boolean.class);
		if (!setMccInResponse || esDto == null) {
			return;
		}

		Optional<TransformedParticipant> transformedParticipant = esDto.getParticipants()
			.stream()
			.filter(participant -> Objects.equals(EntityTypesEnum.MERCHANT.getEntityTypeKey(),
					participant.getEntityType()))
			.findFirst();
		if (transformedParticipant.isPresent()) {
			TransformedParticipant participant = transformedParticipant.get();
			if (participant.getMerchantData() != null
					&& StringUtils.isNotBlank(participant.getMerchantData().getMccCode())) {
				esResponseTxn.setMccCode(participant.getMerchantData().getMccCode());
			}
		}
	}

	private static String getSourceTxnId(final TransformedTransactionHistoryDetail esDto) {
		if (TransactionSource.OMS.getTransactionSourceKey().equals(esDto.getStreamSource())) {
			if (ObjectUtils.isNotEmpty(esDto.getContextMap())
					&& StringUtils.isNotBlank(esDto.getContextMap().get(UPI_TXN_ID))) {
				return esDto.getContextMap().get(UPI_TXN_ID);
			}
		}
		return esDto.getTxnId();
	}

	private static void setSearchFilters(final TransformedTransactionHistoryDetail esDto,
			final EsResponseTxn esResponseTxn) {
		SearchFilters searchFilters = SearchFilters.builder().txnCategory(getSearchTxnCategory(esDto)).build();
		esResponseTxn.setSearchFilters(searchFilters);
	}

	private static Integer getSearchTxnCategory(final TransformedTransactionHistoryDetail esDto) {
		if (esDto.getContextMap() != null && TRUE.equals(esDto.getContextMap().get(IS_SELF_TRANSFER))) {
			return TxnCategoryEnum.SELF_TRANSFER.getTxnCategoryKey();
		}
		// log.info("txnId {} esDto.getSearchFields() {}", esDto.getTxnId(),
		// Objects.nonNull(esDto.getSearchFields()));
		TxnCategoryEnum txnCategoryEnum = TxnCategoryEnum
			.getTxnCategoryEnumFromTxnType(TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType()));

		return Objects.nonNull(txnCategoryEnum) ? txnCategoryEnum.getTxnCategoryKey() : null;
	}

	/**
	 * Sets the instrument information for a given TransformedTransactionHistoryDetail
	 * object. This method adds the InstrumentInfo objects for user participants.
	 * Additionally, if the transaction involves PPBL a/c but doesnt contain ppbl
	 * participant adds PPBL instrument to the result. Finally, the instrumentInfoList is
	 * set as the user instrument information in the EsResponseTxn object.
	 * @param esDto The TransformedTransactionHistoryDetail object.
	 * @param esResponseTxn The EsResponseTxn object.
	 */
	private static void setInstrumentInfo(final TransformedTransactionHistoryDetail esDto,
			final EsResponseTxn esResponseTxn) {
		List<InstrumentInfo> instrumentInfoList = new ArrayList<>();
		TransformedParticipant lastParticipant = null;
		boolean isPpblParticipantPresent = false;
		// Null check on searchPaymentSystem for NPE handling -> PTH-604
		boolean isPpblTxn = esDto != null && esDto.getSearchFields() != null
				&& Objects.nonNull(esDto.getSearchFields().getSearchPaymentSystem())
				&& !esDto.getSearchFields().getSearchPaymentSystem().isEmpty() // Check if
																				// list is
																				// not
																				// empty
				&& esDto.getSearchFields()
					.getSearchPaymentSystem()
					.contains(PaymentSystemEnum.PPBL.getPaymentSystemKey().toString());

		for (TransformedParticipant participant : esDto.getParticipants()) {
			if (EntityTypesEnum.USER.getEntityTypeKey().equals(participant.getEntityType())
					&& esDto.getTxnIndicator().equals(participant.getTxnIndicator())) {
				InstrumentInfo instrumentInfo = null;
				if (lastParticipant == null) {
					lastParticipant = participant;
				}
				// Special Handling1 - PG sends paymentSystem=BANK comes for any txn that
				// involves ppbl
				// sending both instrumentType=2, 8 will be duplicate. hence treating
				// paymentSystem=BANK as paymentSystem=PPBL
				// added isPpblTxn check as txn might have failed with bank in which case
				// searchPaymentSystem wouldnt contain ppbl
				if (isPpblTxn && PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					TransformedParticipant clonedParticipant = SerializationUtils.clone(lastParticipant);
					clonedParticipant.setPaymentSystem(PaymentSystemEnum.PPBL.getPaymentSystemKey());
					instrumentInfo = InstrumentInfoMapper.build(esDto, clonedParticipant);
					isPpblParticipantPresent = true;
				}
				else if (PaymentSystemEnum.BANK.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					// Special Handling 2 - if isPpblTxn is false & paymentSystem=BANK, it
					// means txn failed
					// with bank but successful in retry with another payment system.
					// Hence, no need to add.
					continue;
				}
				else if (UpiLiteUtility.isUpiLiteTxnAndPaymentInstrument(participant)) {
					instrumentInfo = InstrumentInfoMapper.build(esDto, participant);

					/*
					 * The above else if is added because for UPI Lite txns
					 * searchPaymentSystem is UPI Lite but participant.paymentSystem is
					 * UPI. So below else if will never pass
					 */

					// TODO Check if below condition is required also or not
					// Fix - PTH-1075 5th Point Null pointer exception while creating
					// listing response
				}
				else if (esDto.getSearchFields() == null
						|| CollectionUtils.isEmpty(esDto.getSearchFields().getSearchPaymentSystem())
						|| !esDto.getSearchFields()
							.getSearchPaymentSystem()
							.contains(participant.getPaymentSystem().toString())) {
					// BE filter works on searchPaymentSystem. if searchPaymentSystem does
					// not have participant paymentSystem,
					// no need to add instrument. This is needed for cases like txn failed
					// with upi & then retried with wallet.
					continue;
				}
				else {
					instrumentInfo = InstrumentInfoMapper.build(esDto, participant);
				}
				instrumentInfoList.add(instrumentInfo);
				lastParticipant = participant;

				if (PaymentSystemEnum.PPBL.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					isPpblParticipantPresent = true;
				}

			}
		}

		// Special Handling 3 - in case of upi/wallet txn with ppbl a/c, merging might not
		// happen hence need to add ppbl instrument
		if (isPpblTxn && !isPpblParticipantPresent) {
			log.info("Adding ppblParticipant manually");
			TransformedParticipant clonedParticipant = SerializationUtils.clone(lastParticipant);
			if (clonedParticipant != null) {
				clonedParticipant.setPaymentSystem(PaymentSystemEnum.PPBL.getPaymentSystemKey());
				InstrumentInfo ppblInstrumentInfo = InstrumentInfoMapper.build(esDto, clonedParticipant);
				instrumentInfoList.add(ppblInstrumentInfo);
			}
		}
		esResponseTxn.setUserInstrumentInfo(instrumentInfoList);
	}

	private static String getModifiedTxnId(final TransformedTransactionHistoryDetail esDto) {
		String modifieldTxnId = esDto.getTxnId();
		if (ObjectUtils.isNotEmpty(esDto.getTxnDate())) {
			modifieldTxnId = modifieldTxnId + TXN_DATE_SEPARATOR + esDto.getTxnDate();
		}

		if (ObjectUtils.isNotEmpty(esDto.getStreamSource())) {
			modifieldTxnId = modifieldTxnId + STREAM_SOURCE_SEPARATOR
					+ TransactionSource.getTransactionSourceEnumByKey(esDto.getStreamSource());
		}

		if (StringUtils.isNotBlank(esDto.getGroupId())
				&& !(TransactionTypeEnum.P2P_UPI_TO_WALLET_INWARD.getTransactionTypeKey().equals(esDto.getMainTxnType())
						|| TransactionTypeEnum.P2P_UPI_TO_WALLET_OUTWARD.getTransactionTypeKey()
							.equals(esDto.getMainTxnType()))) {
			modifieldTxnId = modifieldTxnId + GRP_ID_SEPARATOR + esDto.getGroupId();
		}
		return modifieldTxnId;
	}

	public static String setBankData(final TransformedTransactionHistoryDetail esDto,
			final SecondPartyInfo secondPartyInfo, final String status,
			final Map<String, UserDetails> userIdImageUrlMapFromCache, final boolean isRecentPageListing,
			final EsResponseTxn esResponseTxn, final boolean isForListingPage) {

		Map<String, String> contextMap = new HashMap<>();
		TransformedParticipant userParticipant = null;
		if (TransactionSource.PPBL.getTransactionSourceKey().equals(esDto.getStreamSource())) {
			for (TransformedParticipant participant : esDto.getParticipants()) {
				if (esDto.getTxnIndicator().equals(participant.getTxnIndicator())
						&& participant.getContextMap() != null) {
					contextMap = participant.getContextMap();
					userParticipant = participant;
				}
			}
			// Setting LogoOrder for Bank Txn
			List<Logo> logoOrder = LogoCreator.getLogoOrderDetails(userParticipant, esDto, userIdImageUrlMapFromCache);
			secondPartyInfo.setLogoOrder(logoOrder);
		}
		else {
			contextMap = esDto.getContextMap();
		}
		String narration = "";
		// https://wiki.mypaytm.com/x/HypjHQ. Point no: 6
		if (("20502".equals(contextMap.get(REPORT_CODE)) && "add-money@paytm".equals(contextMap.get("benefAcctNum")))
				|| ("60202".equals(contextMap.get(REPORT_CODE))
						&& "AddMoneyWallet".equals(contextMap.get("merchantId")))) {
			narration = "Money added to ";
			if (isForListingPage && esResponseTxn != null) {
				esResponseTxn.setListingResponseMappingEnum(ListingResponseMappingEnum.ADD_MONEY);
			}
			if (secondPartyInfo != null) {
				secondPartyInfo.setLogoUrl("https://tpap-logo.paytm.com/uth/images/wallet-logo/add-money-wallet.png");
				secondPartyInfo.setName("Wallet");
			}
			return narration;
		}
		BankConfig bankConfig = BeanUtil.getBean(BankConfig.class);
		RptCodeConfig rptCodeConfig = GenericUtility.fetchRptCodeConfig(bankConfig, contextMap.get(REPORT_CODE),
				esDto.getTxnIndicator());
		narration = rptCodeConfig.getNarration();
		String dynamicNarration = rptCodeConfig.getDynamicNarration();
		if (contextMap != null && contextMap.containsKey(REPORT_CODE)
				&& (fdReportCodeSet.contains(contextMap.get(REPORT_CODE))
						|| upiInternationalReportCodeSet.contains(contextMap.get(REPORT_CODE))
						|| VISA_RECURRING_REPORT_CODE.contains(contextMap.get(REPORT_CODE)))) {
			narration = GenericUtility.getBankNarration(contextMap.get(REPORT_CODE), esDto.getTxnIndicator(), status,
					LISTING);
			dynamicNarration = "NA";
		}

		if (!StringUtils.isEmpty(dynamicNarration) && !"NA".equalsIgnoreCase(dynamicNarration)) {
			dynamicNarration = BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getDynamicNarration(),
					contextMap, bankConfig.getBankStaticPropertiesConfig(), esDto);
		}
		if (StringUtils.isBlank(narration) || StringUtils.isBlank(dynamicNarration)) {
			narration = rptCodeConfig.getDefaultNarration();
			dynamicNarration = rptCodeConfig.getDefaultDynamicNarration();
			if (!StringUtils.isEmpty(dynamicNarration) && !"NA".equalsIgnoreCase(dynamicNarration)) {
				dynamicNarration = BankDataConfigEnum.getStaticKeyFroDynamicKey(dynamicNarration, contextMap,
						bankConfig.getBankStaticPropertiesConfig(), esDto);
			}
		}
		if (StringUtils.isEmpty(narration) || StringUtils.isEmpty(dynamicNarration)) {
			RptCodeConfig defaultConfig = bankConfig.getRptCodeConfig("default", esDto.getTxnIndicator());
			narration = defaultConfig.getNarration();
			dynamicNarration = defaultConfig.getDynamicNarration();
			if (!StringUtils.isEmpty(dynamicNarration) && !"NA".equalsIgnoreCase(dynamicNarration)) {
				dynamicNarration = BankDataConfigEnum.getStaticKeyFroDynamicKey(dynamicNarration, contextMap,
						bankConfig.getBankStaticPropertiesConfig(), esDto);
			}
		}

		String logoUrl = null;
		if (!PPBL_TXN_TYPES_FOR_COMMON_LOGOS
			.contains(TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getTxnType()))) {
			if (TransactionSource.TS.getTransactionSourceKey().equals(esDto.getStreamSource())
					&& fdReportCodeSet.contains(contextMap.get(REPORT_CODE))) {
				logoUrl = BankDataConfigEnum.getStaticKeyFroDynamicKey(rptCodeConfig.getImage(), contextMap,
						bankConfig.getBankStaticPropertiesConfig(), esDto);
			}
			else {
				if (p2mCardLogoRptCodes.contains(contextMap.get(REPORT_CODE))) {
					logoUrl = BankDataConfigEnum.getStaticKeyFroDynamicKey("#{categoryUrl}p2m-card.png", contextMap,
							bankConfig.getBankStaticPropertiesConfig(), esDto);
				}
				else {
					logoUrl = LogoUtility.getBankCategoryLogo(esDto.getTxnType());
				}
			}
		}
		if (secondPartyInfo != null) {
			if (!StringUtils.isEmpty(logoUrl)) {
				secondPartyInfo.setLogoUrl(logoUrl);
			}
			if (!"NA".equalsIgnoreCase(dynamicNarration)) {
				secondPartyInfo.setName(dynamicNarration);
			}
			else if (secondPartyInfo.getName() == null || secondPartyInfo.getName().isBlank()) {
				secondPartyInfo.setName(null);
			}
		}
		String recentNarration = "";
		if (isRecentPageListing && Objects.nonNull(rptCodeConfig.getRecentTransactionInfo())) {
			recentNarration = rptCodeConfig.getRecentTransactionInfo().getRecentNarration();
			if (StringUtils.isNotBlank(recentNarration)) {
				narration = recentNarration;
			}
			if (StringUtils.isBlank(rptCodeConfig.getRecentTransactionInfo().getRecentDynamicNarration())) {
				// secondPartyInfo.setName(null);
			}
		}
		return narration;
	}

	protected static SecondPartyInfo getSecondPartyInfo(final TransformedTransactionHistoryDetail esDto,
			final String status, final Map<String, UserDetails> userIdImageUrlMapFromCache,
			final boolean isDetailPage) {
		SecondPartyInfo secondPartyInfo = GenericUtility.getSecondPartyInfo(esDto, userIdImageUrlMapFromCache);
		String purpose = getTxnPurpose(esDto);
		// to handle default cases.
		TransactionTypeEnum transactionType = TransactionTypeEnum.getTransactionTypeEnumByKey(esDto.getMainTxnType());
		assert transactionType != null;
		if (transactionTypeEnumISecondPartyMapperMap.containsKey(transactionType)) {
			try {
				transactionTypeEnumISecondPartyMapperMap.get(transactionType)
					.populateSecondPartyInfo(esDto, secondPartyInfo, transactionType);
			}
			catch (Exception e) {
				log.error("Error while setting second party Info for  txnid :{}", esDto.getTxnId());
			}
		}

		// handling only activation and deactivation case of UPI Wallet for listing
		if (TransactionTypeEnum.ADD_MONEY_TO_UPI_LITE.equals(transactionType)
				|| TransactionTypeEnum.DEACTIVATION_OF_UPI_LITE.equals(transactionType)) {
			return UpiLiteViewUtility.getSecondPartyInfoForUpiLite(esDto);
		}

		switch (transactionType) {
			case ADD_MONEY:
				if (TransactionSource.WALLET.getTransactionSourceKey().equals(esDto.getStreamSource())) {
					try {
						TransformedWalletData walletData = esDto.getParticipants().get(0).getWalletData();
						String walletType = WalletTypesEnum.getWalletDisplayName(walletData.getWalletType());
						secondPartyInfo.setName(walletType);
					}
					catch (Exception e) {
						log.error("Error while getting wallet type for id :{}", esDto.getTxnId());
						secondPartyInfo.setName(WalletTypesEnum.SCLW.getDisplayName());
					}
				}
				else {
					if (!StringUtils.isEmpty(purpose) && (purpose.equalsIgnoreCase(PAYTM_GIFT_VOUCHER)
							|| purpose.equalsIgnoreCase(PAYTMGIFTVOUCHER))) {
						secondPartyInfo.setName(WalletTypesEnum.GIFT_VOUCHER.getDisplayName());
					}
					else {
						secondPartyInfo.setName(WalletTypesEnum.SCLW.getDisplayName());
					}
				}
				break;
			case CASHBACK_RECEIVED:
				// In cashBack dtos, we get only one participant and get merchant name in
				// contextMap.
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					String merchantName = null;
					if (Objects.nonNull(esDto.getContextMap())) {
						merchantName = esDto.getContextMap().get(MERCHANT_NAME);
					}

					if (StringUtils.isBlank(merchantName)) {
						if (StringUtils.isNotBlank(esDto.getOtherPartyEntityId())) {
							secondPartyInfo.setName(PAYTM_MERCHANT);
						}
						else {
							secondPartyInfo.setName(MERCHANT);
						}
					}
					secondPartyInfo.setName(GenericUtility.formatString(merchantName));
				}
				break;
			case P2M:
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					if (MandateUtility.txnValidForRecurring(esDto)) {
						// secondPartyInfo.setName(null);
					}
					else {
						if (StringUtils.isNotBlank(esDto.getOtherPartyEntityId())) {
							secondPartyInfo.setName(PAYTM_MERCHANT);
						}
						else {
							secondPartyInfo.setName(MERCHANT);
						}
					}
				}
				break;
			case P2M_REFUND:
			case WALLET_UPI_DEBIT_P2M:
			case ON_HOLD:
			case RELEASED:
			case ADD_AND_PAY:
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					if (StringUtils.isNotBlank(esDto.getOtherPartyEntityId())) {
						secondPartyInfo.setName(PAYTM_MERCHANT);
					}
					else {
						secondPartyInfo.setName(MERCHANT);
					}
				}
				break;
			case P2P_OUTWARD_REVERSAL:
				secondPartyInfo.setName("Bank");
				break;
			case P2P_OUTWARD:
			case P2P_INWARD:
			case P2P2M:
			case P2P2M_INWARD:
			case P2P2M_OUTWARD:
			case P2P2M_REFUND:
			case WALLET_UPI_DEBIT_P2P:
			case UPI_WALLET_CREDIT:
			case UPI_WALLET_CREDIT_REVERSAL:
			case WALLET_UPI_DEBIT_REVERSAL:
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					if (StringUtils.isNotBlank(esDto.getOtherPartyEntityId())) {
						secondPartyInfo.setName("Paytm User");
					}
					else {
						secondPartyInfo.setName("User");
					}
				}
				break;
			case P2P_UPI_TO_WALLET_OUTWARD:
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					for (TransformedParticipant participant : esDto.getParticipants()) {
						if (!esDto.getTxnIndicator().equals(participant.getTxnIndicator())) {
							secondPartyInfo.setName(participant.getWalletData().getWalletMobileNumber());
						}
					}
				}
				break;
			case PPBL_TRANSACTION:
				if (isDetailPage) {
					setBankData(esDto, secondPartyInfo, status, null, false, null, false);
				}
				break;
			case P2P_OUTWARD_REMITTANCE:
			case P2P_OUTWARD_REMITTANCE_REFUND:
				if (StringUtils.isBlank(secondPartyInfo.getName())) {
					secondPartyInfo.setName("Paytm User");
				}
				for (TransformedParticipant participant : esDto.getParticipants()) {
					if (!esDto.getTxnIndicator().equals(participant.getTxnIndicator())) {
						getBankMerchantLogoOrder(secondPartyInfo, participant, esDto);
					}
				}
				break;
			default:
		}
		return secondPartyInfo;
	}

	private static void getBankMerchantLogoOrder(final SecondPartyInfo secondPartyInfo,
			final TransformedParticipant participant, final TransformedTransactionHistoryDetail tthd) {
		if (DataValidationUtility.isOtherPartyBankDetailsRequired(tthd)) {
			if (Objects.nonNull(participant.getBankData())) {
				secondPartyInfo.setLogoUrl(LogoUtility.getBankLogo(participant.getBankData().getIfsc(),
						participant.getBankData().getBankName()));
			}
		}
		if (StringUtils.isEmpty(secondPartyInfo.getLogoUrl())) {
			secondPartyInfo.setLogoUrl(participant.getLogoUrl() != null ? participant.getLogoUrl()
					: LogoUtility.getLogo(MERCHANT_DEFAULT_LOGO, LogoType.TRANSACTION_CATEGORY_ICON));
		}
	}

	private static long getDocUpdatedDate(final TransformedTransactionHistoryDetail esDto,
			final Map<String, String> paramMap) {

		/*
		 * Handling Only for Search API with version >= v3 Problem : For many older txn we
		 * get update, which can cause mismatch in recon API This updated Event updates
		 * the doUpdatedDate. In recon we apply query on this field, this can come in its
		 * range and can cause mismatch. Although this was very old txn. Which leads to
		 * invalidate cache.
		 *
		 * Why ToBe handle : This Event don't add any value to existing doc, it almost
		 * useless. currently we entertain such event with limit of 180 days. So we'll be
		 * handling this.
		 *
		 * setDocUpdatedDate when its diff with txnDate is less than equal to D days. if
		 * condition fails then set txnDate in place of updateDate.
		 */

		if (UtilityExtension.isSearchApiRequestWithV3OrAbove(paramMap)) {
			if (isDateWithinLimit(esDto.getDocUpdatedDate(), esDto.getTxnDate())) {
				return esDto.getDocUpdatedDate();
			}
			else {
				log.info("Passing txnDate in docUpdatedDate field of search Response");
				return esDto.getTxnDate();
			}
		}
		else {
			return esDto.getDocUpdatedDate();
		}
	}

	public static boolean isDateWithinLimit(final Long dateToCheck, final Long dateToCheckWith) {
		// This func will check date with currentDate and then compare with allowed diff.
		long allowedDiffInDays = configurablePropertiesHolder.getProperty(SEARCH_API_UPDATES_DATE_SET_WHEN_DIFF_IN_DAYS,
				Long.class);
		return DateTimeUtility.isDateWithinLimit(dateToCheck, dateToCheckWith, allowedDiffInDays);
	}

}
