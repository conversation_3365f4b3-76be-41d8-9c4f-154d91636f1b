package com.org.panaroma.web.client.oauth;

import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.CircuitBreakerProperties.UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_FAILURE_HTTPCODES;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.UthDownStreamName.AUTH_DOWNSTREAM_NAME;
import static com.org.panaroma.commons.constants.ConfigurationPropertiesConstants.UthDownStreamName.USE_BANK_OAUTH;
import static com.org.panaroma.commons.constants.WebConstants.AUTHORIZATION;
import static com.org.panaroma.commons.constants.WebConstants.DEFAULT_CUSTOMER_CREATION_DATE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_NAME;
import static com.org.panaroma.web.monitoring.MonitoringConstants.API_TYPE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.BANK_AUTH;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COLON;
import static com.org.panaroma.web.monitoring.MonitoringConstants.COMMA;
import static com.org.panaroma.web.monitoring.MonitoringConstants.EMPTY_RESPONSE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.OAUTH_RESPONSE_COUNT;
import static com.org.panaroma.web.monitoring.MonitoringConstants.OCL_AUTH;
import static com.org.panaroma.web.monitoring.MonitoringConstants.RESPONSE_CODE;
import static com.org.panaroma.web.monitoring.MonitoringConstants.STATUS;

import com.org.panaroma.commons.dto.com.org.bank.OauthUserInformation;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.rollout.strategy.IRolloutStrategyHelper;
import com.org.panaroma.web.circuit.breaker.AuthDownStreamCircuitBreakerUtil;
import com.org.panaroma.web.configuration.oauthClient.OauthClientConnProviderProp;
import com.org.panaroma.web.dto.CustomOauthUserDetailsResponse;
import com.org.panaroma.web.dto.OauthTokenInfoResponse;
import com.org.panaroma.web.dto.PrincipalOauthResponse;
import com.org.panaroma.web.exceptionhandler.ExceptionHandlerUtil;
import com.org.panaroma.web.monitoring.MetricsAgent;
import com.org.panaroma.web.utility.LogHelper;
import com.org.panaroma.web.utility.configurablePropertyUtility.ConfigurablePropertiesHolder;
import io.netty.channel.ChannelOption;
import io.netty.channel.ConnectTimeoutException;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.TimeoutException;
import java.net.SocketTimeoutException;
import java.net.http.HttpTimeoutException;
import java.nio.charset.Charset;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ClientHttpConnector;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.client.reactive.ReactorResourceFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.tcp.TcpClient;

@Log4j2
@Component
@Getter
@Setter
public class OauthClient implements IOauthClient {

	private String oauthIp;

	private String clientId;

	private String clientSecret;

	private Integer connectionTimeOut;

	private Integer readTimeOut;

	private String bankOauthClientId;

	private String bankOauthClientSecret;

	private String bankOauthBaseUrl;

	private String bankOauthCategoryUrl;

	private boolean useBankOauth;

	private boolean isBankAuthRequest;

	// private WebClient webClient;
	private String oauthTokenHeader;

	private OauthClientConnProviderProp oauthClientConnProviderProp;

	private boolean oauthTokenEvaluationSkip;

	private static final String REQUEST_TOKEN = "Request-Token";

	@Autowired
	private MetricsAgent metricsAgent;

	@Autowired
	private AuthDownStreamCircuitBreakerUtil authDownStreamCircuitBreakerUtil;

	@Autowired
	private ConfigurablePropertiesHolder propertiesConfig;

	private final IRolloutStrategyHelper rolloutStrategyHelper;

	private final ClientHttpConnector connector;

	@Autowired
	public OauthClient(@Value("${oauth.service.base.url}") final String oauthIp,
			@Value("${oauth.client.id}") final String clientId,
			@Value("${oauth.client.secret}") final String clientSecret,
			@Value("${oauth.client.connection.timeout}") final String connectTimeout,
			@Value("${oauth.read.timeout}") final String readTimeOut,
			@Value("${bank.oauth.client.id}") final String bankOauthClientId,
			@Value("${bank.oauth.client.secret}") final String bankOauthClientSecret,
			@Value("${bank.oauth.service.base.url}") final String bankOauthBaseUrl,
			@Value("${bank.oauth.service.url.category}") final String bankOauthCategoryUrl,
			@Value("${use-bank-oauth}") final boolean useBankOauth,
			@Value("${oauth.token.evaluation.skip}") final boolean oauthTokenEvaluationSkip,
			final OauthClientConnProviderProp oauthClientConnProviderProp,
			final IRolloutStrategyHelper rolloutStrategyHelper) {
		this.oauthIp = oauthIp;
		this.clientId = clientId;
		this.clientSecret = clientSecret;
		this.readTimeOut = Integer.parseInt(readTimeOut);
		this.connectionTimeOut = Integer.parseInt(connectTimeout);
		this.useBankOauth = useBankOauth;
		this.bankOauthClientId = bankOauthClientId;
		this.bankOauthClientSecret = bankOauthClientSecret;
		this.bankOauthBaseUrl = bankOauthBaseUrl;
		this.bankOauthCategoryUrl = bankOauthCategoryUrl;
		this.oauthClientConnProviderProp = oauthClientConnProviderProp;
		this.oauthTokenEvaluationSkip = oauthTokenEvaluationSkip;
		this.rolloutStrategyHelper = rolloutStrategyHelper;
		this.connector = null;
		this.oauthTokenHeader = null;
	}

	@Override
	public Mono<CustomOauthUserDetailsResponse> getUserDetailsFromOauth(final String token, final String fetchStrategy,
			final String requestToken) {

		if (oauthTokenEvaluationSkip && token.length() <= 15) {
			CustomOauthUserDetailsResponse userDetailsResponse = new CustomOauthUserDetailsResponse();
			userDetailsResponse.setStatus(null);
			userDetailsResponse.setUserId(token);
			OauthUserInformation oauthUserInformation = new OauthUserInformation();
			// setting default value for local as this will be required when we will be
			// using v2/user api
			oauthUserInformation.setCustomerCreationDate(DEFAULT_CUSTOMER_CREATION_DATE);
			userDetailsResponse.setDefaultInfo(oauthUserInformation);
			return Mono.just(userDetailsResponse);
		}

		String endpoint = getEndpoint();

		log.info("Request received to oauth client for fetching userid. oauthUrl : {}. requestId : {}", endpoint,
				requestToken);

		WebClient webClient = getClient(endpoint);
		return webClient.get()
			.uri("/v2/user?fetch_strategy=" + fetchStrategy)
			.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
			.header("data", token)
			.header("verification_type", "oauth_token")
			.header(REQUEST_TOKEN, requestToken)
			.header("authorization", this.oauthTokenHeader)
			.accept(MediaType.APPLICATION_JSON)
			.retrieve()
			.onStatus(status -> status.value() != HttpStatus.OK.value(), response -> {
				List<String> failureHttpCodes = propertiesConfig
					.getProperty(UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_FAILURE_HTTPCODES, ArrayList.class);
				log.error("Auth Status code :{} failureHttpCodes:{}", response.statusCode().value(), failureHttpCodes);
				if (failureHttpCodes != null && failureHttpCodes.contains(response.statusCode().value() + "")) {
					log.error("Circuit Breaker failure count increase for v2/user api for natty");
					authDownStreamCircuitBreakerUtil.incrementFailureCount(AUTH_DOWNSTREAM_NAME);
				}
				ExceptionHandlerUtil.handleOauthValidationException(response.statusCode().value());
				return Mono.empty();
			})
			.bodyToMono(CustomOauthUserDetailsResponse.class)
			.timeout(Duration.ofSeconds(1L))
			.doOnError(ex -> LogHelper.logOnError(e -> {
				if (ex instanceof ConnectTimeoutException || ex instanceof SocketTimeoutException
						|| ex instanceof TimeoutException || ex instanceof HttpTimeoutException) {
					log.error("Exception in Oauth client Exchange method: {}, requestId : {}",
							CommonsUtility.exceptionFormatterWithoutStacktrace((Exception) ex), requestToken);
					authDownStreamCircuitBreakerUtil.incrementFailureCount(AUTH_DOWNSTREAM_NAME);
				}
				else {
					log.error("Exception in Oauth client Exchange method: {}, requestId : {}",
							CommonsUtility.exceptionFormatterWithoutStacktrace((Exception) ex), requestToken);
				}
			}))
			.map(data -> {
				pushOauthMetrics(data);
				return data;
			});
	}

	@Override
	public Mono<OauthTokenInfoResponse> getUserDetailsFromOauth(final String token, final String requestToken) {

		if (oauthTokenEvaluationSkip && token.length() <= 15) {
			PrincipalOauthResponse principal = new PrincipalOauthResponse();
			principal.setUserId(token);
			OauthTokenInfoResponse response = new OauthTokenInfoResponse();
			response.setPrincipal(principal);
			return Mono.just(response);
		}

		String endpoint = getEndpoint();

		log.info("Request received to oauth client for fetching userid. oauthUrl : {}. requestId : {}", endpoint,
				requestToken);

		WebClient webClient = getClient(endpoint);

		return webClient.get()
			.uri("/oauth2/tokeninfo")
			.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
			.header(AUTHORIZATION, this.oauthTokenHeader)
			.header("access_token", token)
			.header(REQUEST_TOKEN, requestToken)
			.accept(MediaType.APPLICATION_JSON)
			.retrieve()
			.onStatus(status -> status.value() != HttpStatus.OK.value(), response -> {
				List<String> failureHttpCodes = propertiesConfig
					.getProperty(UTH_DOWNSTREAM_AUTH_CIRCUIT_BREAKER_FAILURE_HTTPCODES, ArrayList.class);
				log.error("Auth Status code :{} failureHttpCodes:{}", response.statusCode().value(), failureHttpCodes);
				if (failureHttpCodes != null && failureHttpCodes.contains(response.statusCode().value() + "")) {
					log.error("Circuit Breaker failure count increase for tokeninfo api for natty");
					authDownStreamCircuitBreakerUtil.incrementFailureCount(AUTH_DOWNSTREAM_NAME);
				}
				ExceptionHandlerUtil.handleOauthValidationException(response.statusCode().value());
				return Mono.empty();
			})
			.bodyToMono(OauthTokenInfoResponse.class)
			.timeout(Duration.ofSeconds(1L))
			.doOnError(ex -> LogHelper.logOnError(e -> {
				log.warn("Exception in Oauth client Exchange method: {}, requestId : {}",
						CommonsUtility.exceptionFormatterWithoutStacktrace((Exception) ex), requestToken);
				if (ex instanceof ConnectTimeoutException || ex instanceof SocketTimeoutException
						|| ex instanceof TimeoutException || ex instanceof HttpTimeoutException) {
					authDownStreamCircuitBreakerUtil.incrementFailureCount(AUTH_DOWNSTREAM_NAME);
				}
			}))
			.map(data -> {
				pushOauthMetrics(data);
				return data;
			});
	}

	private void pushOauthMetrics(final CustomOauthUserDetailsResponse data) {
		String status;
		String responseCode;
		String apiName = this.isBankAuthRequest() ? BANK_AUTH : OCL_AUTH;
		if (Objects.nonNull(data)) {
			status = data.getStatus();
			responseCode = String.valueOf(data.getResponseCode());
		}
		else {
			status = EMPTY_RESPONSE;
			responseCode = EMPTY_RESPONSE;
		}
		String tag = RESPONSE_CODE + COLON + responseCode + COMMA + STATUS + COLON + status + COMMA + API_NAME + COLON
				+ apiName + API_TYPE + COLON + "v2/user";
		metricsAgent.incrementCount(OAUTH_RESPONSE_COUNT, tag);
	}

	private void pushOauthMetrics(final OauthTokenInfoResponse data) {
		String apiName = this.isBankAuthRequest() ? BANK_AUTH : OCL_AUTH;
		String tag = API_NAME + COLON + apiName + API_TYPE + COLON + "/oauth2/tokeninfo";
		metricsAgent.incrementCount(OAUTH_RESPONSE_COUNT, tag);
	}

	private String createAuthHeader() {
		if (useBankOauth) {
			return getBankAuthApiHeader();
		}
		else {
			return getOclAuthApiHeader();
		}
	}

	private String getOclAuthApiHeader() {
		String id = clientId;
		String secret = clientSecret;
		String auth = id + ":" + secret;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		return authHeader;
	}

	private String getBankAuthApiHeader() {
		String id = bankOauthClientId;
		String secret = bankOauthClientSecret;
		String auth = id + ":" + secret;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
		String authHeader = "Basic " + new String(encodedAuth);
		return authHeader;
	}

	private String getEndpoint() {
		StringBuilder endpoint = new StringBuilder();

		if (propertiesConfig.getProperty(USE_BANK_OAUTH, Boolean.class)
				&& rolloutStrategyHelper.isUserWhiteListed("bankOauthApiRollout", "")) {
			endpoint.append(getBankOauthUrl(this.bankOauthBaseUrl, this.bankOauthCategoryUrl));
			setOauthTokenHeader(getBankAuthApiHeader());
			setBankAuthRequest(true);
		}
		else {
			endpoint.append(this.oauthIp);
			setOauthTokenHeader(getOclAuthApiHeader());
			setBankAuthRequest(false);
		}
		return endpoint.toString();
	}

	private WebClient getClient(final String clientIp) {
		return this.createOauthClient(clientIp);
	}

	private String getBankOauthUrl(final String bankOauthBaseUrl, final String bankOauthCategoryUrl) {
		return bankOauthBaseUrl + bankOauthCategoryUrl;
	}

	private ClientHttpConnector getConnector() {
		ReactorResourceFactory resourceFactory = new ReactorResourceFactory();
		resourceFactory.setUseGlobalResources(false);
		ConnectionProvider connectionProvider = ConnectionProvider
			.builder(oauthClientConnProviderProp.getConnProviderName())
			.maxConnections(oauthClientConnProviderProp.getConnProviderMaxConnections())
			.pendingAcquireTimeout(Duration.ofMillis(oauthClientConnProviderProp.getConnProviderAcquireTimeout()))
			.maxIdleTime(Duration.of(oauthClientConnProviderProp.getConnProviderMaxIdleTime(), ChronoUnit.SECONDS))
			.build();
		resourceFactory.setConnectionProvider(connectionProvider);
		resourceFactory.afterPropertiesSet();

		TcpClient tcpClient = TcpClient.create(resourceFactory.getConnectionProvider())
			.runOn(resourceFactory.getLoopResources())
			.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, this.connectionTimeOut)
			.doOnConnected((connection) -> connection
				.addHandlerLast(new ReadTimeoutHandler(this.readTimeOut, TimeUnit.MILLISECONDS)));

		return new ReactorClientHttpConnector(HttpClient.from(tcpClient));
	}

	private WebClient createOauthClient(final String oauthIp) {
		return WebClient.builder().baseUrl(oauthIp).clientConnector(connector).build();
		// return WebClient.create(oauthIp);
	}

	// public void setOauthHeader() {
	// this.oauthTokenHeader = createAuthHeader();
	// }

	// @Override
	// public Mono<UserDetailsResponse> getUserDetailsViaEIS(String token, String
	// fetchStrategy,
	// String txnId) {
	// log.debug("Request received to oauth client for fetching user id.");
	// Mono<ClientResponse> responseMono = this.eisClient.get().
	// uri("/v2/user?fetch_strategy=" + fetchStrategy)
	// .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
	// .header(REQUEST_TOKEN, txnId).header(OAUTH_TOKEN, token)
	// .accept(MediaType.APPLICATION_JSON)
	// .exchange();
	// return responseMono.flatMap(t -> {
	// Mono<CustomResponse> c = t.bodyToMono(CustomResponse.class);
	// return c.flatMap(this::convertResponse);
	// }
	// );
	// }
	//
	// @Override
	// public Mono<OauthUserDetailsResponse> getUserDetailsFromOauth(String token, String
	// fetchStrategy,
	// String txnId) {
	// log.debug("Request received to oauth client for fetching userid. oauthUrl : {}",
	// oauthIp);
	//
	// return this.oauthClient.get()
	// .uri("/v2/user?fetch_strategy=" + fetchStrategy)
	// .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
	// .header("data", token)
	// .header("verification_type", "oauth_token")
	// .header("authorization", this.oauthTokenHeader)
	// .accept(MediaType.APPLICATION_JSON).retrieve().bodyToMono(OauthUserDetailsResponse.class)
	// .doOnError(ex -> LogHelper
	// .logOnError(e -> log.error("Exception in Oauth client Exchange method: {}", ex)));
	// }
	//
	// @Override
	// public Mono<OauthUserDetailsResponse> getUserDetailsFromBankOauth(String token,
	// String fetchStrategy, String txnId) {
	// log.debug("Request received to oauth client for fetching userid. oauthUrl : {}",
	// this.bankOauthBaseUrl);
	//
	// return this.bankOauthClient.get()
	// .uri("/v2/user?fetch_strategy=" + fetchStrategy)
	// .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
	// .header("data", token)
	// .header("verification_type", "oauth_token")
	// .header("authorization", this.bankOauthTokenHeader)
	// .accept(MediaType.APPLICATION_JSON).retrieve().bodyToMono(OauthUserDetailsResponse.class)
	// .doOnError(ex -> LogHelper
	// .logOnError(e -> log.error("Exception in Oauth client Exchange method: {}", ex)));
	// }

	// private Mono<UserDetailsResponse> convertResponse(CustomResponse customResponse) {
	// try {
	//
	// log.debug("Response received from oauth: {}.", customResponse);
	// if (customResponse.getStatus().equals(Status.SUCCESS)) {
	// UserDetailsResponse userDetailsResponse = objectMapper
	// .convertValue(customResponse.getResponse(),
	// UserDetailsResponse.class);
	// log.info("User Id: {} fetched from Oauth.", userDetailsResponse.getUserId());
	// return Mono.just(userDetailsResponse);
	// } else {
	// FailureResponse failureResponse = objectMapper
	// .convertValue(customResponse.getResponse(), FailureResponse.class);
	// log.info("Failed to verify oauth failure response: {}", failureResponse);
	// throw ExceptionFactory.getException(PANAROMA_SERVICE, INVALID_USER_TOKEN).get();
	// }
	// } catch (Exception e) {
	// log.error("Internal server error: {} in Oauth Client.", e);
	// throw e;
	// }
	// }

}
